{"format_version": "1.8.0", "minecraft:client_entity": {"description": {"identifier": "pa:aligatorcroc", "materials": {"default": "axolotl", "limbs": "axolotl_limbs"}, "textures": {"blue": "textures/entity/pamobile/pa_aligatorcroc", "cyan": "textures/entity/pamobile/pa_aligatorcroc", "gold": "textures/entity/pamobile/pa_aligatorcroc", "lucy": "textures/entity/pamobile/pa_aligatorcroc", "wild": "textures/entity/pamobile/pa_aligatorcroc"}, "geometry": {"default": "geometry.pa_aligatorcroc"}, "animations": {"idle_float": "animation.pa_aligatorcroc.idle_underwater", "idle_floor": "animation.pa_aligatorcroc.look_at_target", "idle_floor_water": "animation.pa_aligatorcroc.idle_floor_underwater", "swim": "animation.pa_aligatorcroc.swim", "walk_floor": "animation.pa_aligatorcroc.walk_floor", "walk_floor_water": "animation.pa_aligatorcroc.walk_floor_underwater", "play_dead": "animation.pa_aligatorcroc.play_dead", "swim_angle": "animation.pa_aligatorcroc.swim_angle", "look_at_target": "animation.pa_aligatorcroc.look_at_target"}, "scripts": {"pre_animation": ["variable.moving = query.ground_speed > 0 || query.vertical_speed > 0;", "variable.pitch = query.body_x_rotation;"]}, "animation_controllers": [{"general": "controller.animation.axolotl.general"}, {"move": "controller.animation.axolotl.move"}], "render_controllers": ["controller.render.axolotl"], "spawn_egg": {"texture": "pa:aligatorcroc", "texture_index": 0}}}}