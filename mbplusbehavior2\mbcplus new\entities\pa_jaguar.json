{"format_version": "1.8.0", "minecraft:entity": {"description": {"identifier": "pa:jaguar", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"minecraft:spider_jockey": {"minecraft:addrider": {"entity_type": "minecraft:skeleton"}, "minecraft:rideable": {"seat_count": 1, "family_types": ["skeleton"], "seats": {"position": [0.0, 0.54, 0.0]}}}, "minecraft:spider_stray_jockey": {"minecraft:addrider": {"entity_type": "minecraft:skeleton.stray"}, "minecraft:rideable": {"seat_count": 1, "family_types": ["skeleton"], "seats": {"position": [0.0, 0.54, 0.0]}}}, "minecraft:spider_wither_jockey": {"minecraft:addrider": {"entity_type": "minecraft:skeleton.wither"}, "minecraft:rideable": {"seat_count": 1, "family_types": ["skeleton"], "seats": {"position": [0.0, 0.54, 0.0]}}}, "minecraft:spider_neutral": {"minecraft:environment_sensor": {"on_environment": {"filters": {"test": "is_brightness", "operator": "<", "value": 0.49}, "event": "minecraft:become_hostile"}}, "minecraft:on_target_acquired": {"event": "minecraft:become_angry"}}, "minecraft:spider_hostile": {"minecraft:environment_sensor": {"on_environment": {"filters": {"test": "is_brightness", "operator": ">", "value": 0.49}, "event": "minecraft:become_neutral"}}, "minecraft:on_target_acquired": {"event": "minecraft:become_angry"}, "minecraft:behavior.nearest_attackable_target": {"priority": 3, "within_radius": 99999999, "must_reach": false, "entity_types": [{"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_family", "subject": "other", "value": "zombie_villager"}, {"test": "is_family", "subject": "other", "value": "zombie_pigman"}, {"test": "is_family", "subject": "other", "value": "illager"}, {"test": "is_family", "subject": "other", "value": "vex"}, {"test": "is_family", "subject": "other", "value": "pillager"}, {"test": "is_family", "subject": "other", "value": "fish"}, {"test": "is_family", "subject": "other", "value": "irongolem"}, {"test": "is_family", "subject": "other", "value": "pig"}, {"test": "is_family", "subject": "other", "value": "sheep"}, {"test": "is_family", "subject": "other", "value": "skeleton"}, {"test": "is_family", "subject": "other", "value": "skeletonhorse"}, {"test": "is_family", "subject": "other", "value": "vindicator"}, {"test": "is_family", "subject": "other", "value": "panda"}, {"test": "is_family", "subject": "other", "value": "cow"}, {"test": "is_family", "subject": "other", "value": "creeper"}, {"test": "is_family", "subject": "other", "value": "zombie"}]}, "max_dist": 37, "must_see": false}], "must_see": false, "speed_multiplier": 1.0}}, "minecraft:spider_angry": {"minecraft:angry": {"duration": 10, "duration_delta": 3, "calm_event": {"event": "minecraft:become_calm", "target": "self"}}, "minecraft:behavior.leap_at_target": {"priority": 4, "yd": 0.4, "must_be_on_ground": false}, "minecraft:behavior.melee_attack": {"priority": 3, "speed_multiplier": 1.0, "track_target": true, "reach_multiplier": 0.8}}}, "components": {"minecraft:nameable": {"alwaysShow": false, "allowNameTagRenaming": false}, "minecraft:type_family": {"family": ["spider", "monster", "arthropod"]}, "minecraft:breathable": {"totalSupply": 15, "suffocateTime": 0}, "minecraft:loot": {"table": "loot_tables/entities/spider.json"}, "minecraft:collision_box": {"height": 0.5, "width": 1.1}, "minecraft:health": {"value": 37, "max": 37}, "minecraft:movement": {"value": 0.58}, "minecraft:navigation.climb": {"can_path_over_water": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:attack": {"damage": 7}, "minecraft:behavior.float": {"priority": 1}, "minecraft:behavior.mount_pathing": {"priority": 5, "speed_multiplier": 1.25, "target_dist": 0.0, "track_target": true}, "minecraft:behavior.random_stroll": {"priority": 6, "speed_multiplier": 0.8}, "minecraft:behavior.look_at_player": {"priority": 7, "look_distance": 6.0, "probability": 0.02}, "minecraft:behavior.random_look_around": {"priority": 7}, "minecraft:behavior.hurt_by_target": {"priority": 3}, "minecraft:physics": {}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 10 : 0"}, "minecraft:underwater_movement": {"value": 0.02}, "minecraft:scale": {"value": 0.8}, "minecraft:on_death": {"event": "on:death", "target": "self"}, "minecraft:on_hurt": {"event": "on:hurt", "target": "self"}, "minecraft:on_hurt_by_player": {"event": "on:hurt_by_player", "target": "self"}, "minecraft:on_ignite": {"event": "on:ignite", "target": "self"}, "minecraft:on_target_acquired": {"event": "on:target_acquired", "target": "self"}, "minecraft:on_target_escape": {"event": "on:target_escape", "target": "self"}, "minecraft:on_wake_with_owner": {"event": "on:wake_with_owner", "target": "self"}, "minecraft:behavior.melee_attack": {"priority": 2, "speed_multiplier": 1.0, "target_dist": 0.0, "max_dist": 3, "random_stop_interval": 100, "track_target": false, "reach_multiplier": 1.4}, "minecraft:behavior.nearest_attackable_target": {"priority": 3, "within_radius": 99999999, "must_reach": false, "entity_types": [{"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_family", "subject": "other", "value": "zombie_villager"}, {"test": "is_family", "subject": "other", "value": "zombie_pigman"}, {"test": "is_family", "subject": "other", "value": "illager"}, {"test": "is_family", "subject": "other", "value": "vex"}, {"test": "is_family", "subject": "other", "value": "pillager"}, {"test": "is_family", "subject": "other", "value": "fish"}, {"test": "is_family", "subject": "other", "value": "irongolem"}, {"test": "is_family", "subject": "other", "value": "pig"}, {"test": "is_family", "subject": "other", "value": "sheep"}, {"test": "is_family", "subject": "other", "value": "skeleton"}, {"test": "is_family", "subject": "other", "value": "skeletonhorse"}, {"test": "is_family", "subject": "other", "value": "vindicator"}, {"test": "is_family", "subject": "other", "value": "panda"}, {"test": "is_family", "subject": "other", "value": "cow"}, {"test": "is_family", "subject": "other", "value": "creeper"}, {"test": "is_family", "subject": "other", "value": "zombie"}]}, "max_dist": 37, "must_see": false}], "must_see": false, "speed_multiplier": 1.0}, "minecraft:behavior.owner_hurt_by_target": {"priority": 1}, "minecraft:behavior.owner_hurt_target": {"priority": 2}, "minecraft:behavior.leap_at_target": {"priority": 4, "yd": 0.5, "must_be_on_ground": false, "target_dist": 0.4, "speed_multiplier": 1.0}, "minecraft:fall_damage": {"value": 0.0}, "minecraft:knockback_resistance": {"value": 1.0, "max": 1.0}}, "events": {"minecraft:entity_spawned": {"randomize": [{"weight": 1, "randomize": [{"weight": 80, "filters": {"all_of": [{"test": "is_daytime", "value": false}, {"test": "is_snow_covered", "value": true}, {"test": "is_underground", "value": false}]}, "remove": {}, "add": {"component_groups": ["minecraft:spider_stray_jockey", "minecraft:spider_neutral"]}}, {"weight": 80, "filters": {"test": "is_biome", "value": "the_nether"}, "remove": {}, "add": {"component_groups": ["minecraft:spider_wither_jockey", "minecraft:spider_neutral"]}}, {"weight": 20, "filters": {"any_of": [{"test": "is_daytime", "value": false}, {"test": "is_underground", "value": true}]}, "remove": {}, "add": {"component_groups": ["minecraft:spider_jockey", "minecraft:spider_neutral"]}}]}, {"weight": 99, "remove": {}, "add": {"component_groups": ["minecraft:spider_neutral"]}}]}, "minecraft:become_hostile": {"remove": {"component_groups": ["minecraft:spider_neutral"]}, "add": {"component_groups": ["minecraft:spider_hostile"]}}, "minecraft:become_neutral": {"remove": {"component_groups": ["minecraft:spider_hostile"]}, "add": {"component_groups": ["minecraft:spider_neutral"]}}, "minecraft:become_angry": {"add": {"component_groups": ["minecraft:spider_angry"]}}, "minecraft:become_calm": {"remove": {"component_groups": ["minecraft:spider_angry"]}}, "on:death": {"run_command": {"command": []}}, "on:hurt": {"run_command": {"command": []}}, "on:hurt_by_player": {"run_command": {"command": []}}, "on:ignite": {"run_command": {"command": []}}, "on:target_acquired": {"run_command": {"command": []}}, "on:target_escape": {"run_command": {"command": []}}, "on:wake_with_owner": {"run_command": {"command": []}}}}}