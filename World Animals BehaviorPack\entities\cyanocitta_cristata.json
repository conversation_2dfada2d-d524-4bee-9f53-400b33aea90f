{"format_version": "1.16.0", "minecraft:entity": {"description": {"identifier": "worldanimals:cyanocitta_cristata", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"minecraft:cyanocitta_cristata_baby": {"minecraft:is_baby": {}, "minecraft:scale": {"value": 0.5}, "minecraft:ageable": {"duration": 1200, "feed_items": ["fortniteaddon:banana", "minecraft:melon_slice", "minecraft:sweet_berries"], "grow_up": {"event": "minecraft:ageable_grow_up", "target": "self"}}}, "minecraft:cyanocitta_cristata_adult": {"minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? Math.Random(1,3) : 0"}, "minecraft:loot": {"table": "loot_tables/entities/plumas.json"}, "minecraft:behavior.breed": {"priority": 3, "speed_multiplier": 1.0}, "minecraft:breedable": {"require_tame": true, "breeds_with": {"mate_type": "worldanimals:cyanocitta_cristata", "baby_type": "worldanimals:cyanocitta_cristata", "breed_event": {"event": "minecraft:entity_born", "target": "baby"}}, "breed_items": ["fortniteaddon:banana", "minecraft:melon_slice", "minecraft:sweet_berries"]}}, "minecraft:cyanocitta_cristata_wild": {"minecraft:type_family": {"family": ["cyanocitta_cristata_wild", "mob"]}, "minecraft:tameable": {"probability": 1.0, "tame_items": ["worldanimals:collar"], "tame_event": {"event": "minecraft:on_tame", "target": "self"}}, "minecraft:behavior.random_fly": {"priority": 2, "xz_dist": 15, "y_dist": 1, "y_offset": 0, "speed_multiplier": 1.0, "can_land_on_trees": true, "avoid_damage_blocks": true}, "minecraft:behavior.follow_mob": {"priority": 3, "speed_multiplier": 1.0, "stop_distance": 3, "search_range": 20}}, "minecraft:cyanocitta_cristata_tame": {"minecraft:type_family": {"family": ["cyanocitta_cristata_tame", "mob"]}, "minecraft:is_tamed": {}, "minecraft:behavior.follow_owner": {"priority": 2, "speed_multiplier": 1.0, "start_distance": 5, "stop_distance": 1}, "minecraft:sittable": {}, "minecraft:behavior.stay_while_sitting": {"priority": 1}, "minecraft:behavior.find_mount": {"priority": 3, "within_radius": 16, "avoid_water": true, "start_delay": 100, "target_needed": false, "mount_distance": 2.0}}}, "components": {"minecraft:breathable": {"total_supply": 15, "suffocate_time": 0}, "minecraft:collision_box": {"width": 0.5, "height": 1}, "minecraft:nameable": {}, "minecraft:health": {"value": 6, "max": 6}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:movement": {"value": 0.4}, "minecraft:damage_sensor": {"triggers": {"cause": "fall", "deals_damage": false}}, "minecraft:jump.static": {}, "minecraft:can_fly": {"value": true}, "minecraft:navigation.fly": {"can_path_over_water": true, "can_path_from_air": true}, "minecraft:movement.fly": {}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.panic": {"priority": 0, "speed_multiplier": 1.25}, "minecraft:behavior.look_at_player": {"priority": 1, "look_distance": 8.0}, "minecraft:healable": {"force_use": true, "filters": {"test": "is_riding", "operator": "!=", "value": true}, "items": [{"item": "cookie", "heal_amount": 0, "effects": [{"name": "fatal_poison", "chance": 1.0, "duration": 1000, "amplifier": 0}]}]}, "minecraft:leashable": {"soft_distance": 4.0, "hard_distance": 6.0, "max_distance": 10.0}}, "events": {"minecraft:entity_spawned": {"randomize": [{"weight": 100, "add": {"component_groups": ["minecraft:cyanocitta_cristata_adult", "minecraft:cyanocitta_cristata_wild"]}}]}, "minecraft:entity_born": {"remove": {}, "add": {"component_groups": ["minecraft:cyanocitta_cristata_baby", "minecraft:cyanocitta_cristata_tame"]}}, "minecraft:ageable_grow_up": {"remove": {"component_groups": ["minecraft:cyanocitta_cristata_baby"]}, "randomize": [{"weight": 5, "remove": {}, "add": {"component_groups": ["minecraft:male_cyanocitta_cristata", "minecraft:cyanocitta_cristata_adult"]}}, {"weight": 5, "remove": {}, "add": {"component_groups": ["minecraft:female_cyanocitta_cristata", "minecraft:cyanocitta_cristata_adult"]}}]}, "minecraft:on_tame": {"remove": {"component_groups": ["minecraft:cyanocitta_cristata_wild"]}, "add": {"component_groups": ["minecraft:cyanocitta_cristata_tame"]}}}}}