{"format_version": "1.10.0", "minecraft:attachable": {"description": {"identifier": "worldanimals:reptil_helmet.player", "item": {"worldanimals:reptil_helmet": "query.owner_identifier == 'minecraft:player'"}, "materials": {"default": "armor", "enchanted": "armor_enchanted"}, "textures": {"default": "textures/entity/reptils/crocodile_v2", "enchanted": "textures/misc/enchanted_item_glint"}, "geometry": {"default": "geometry.crocodile_mask"}, "scripts": {"animate": ["loop_controller", {"run": "q.is_sprinting"}], "parent_setup": "variable.boot_layer_visible = 0.0;"}, "render_controllers": ["controller.render.armor"], "animations": {"loop_controller": "animation.crocodile_mask.idle", "run": "animation.crocodile_mask.run"}}}}