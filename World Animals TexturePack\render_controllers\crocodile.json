{"format_version": "1.8.0", "render_controllers": {"controller.render.crocodile": {"arrays": {"textures": {"Array.decor": ["Texture.default"]}}, "geometry": "Geometry.default", "part_visibility": [{"*": "query.variant == 0"}], "materials": [{"*": "Material.default"}], "textures": ["Array.decor[query.variant]"]}, "controller.render.white_crocodile": {"arrays": {"textures": {"Array.decor": ["Texture.white_crocodile"]}}, "geometry": "Geometry.default", "part_visibility": [{"*": "query.variant == 1"}], "materials": [{"*": "Material.default"}], "textures": ["Array.decor[query.variant]"]}}}