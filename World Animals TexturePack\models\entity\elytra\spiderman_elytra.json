{"format_version": "1.10.0", "geometry.spiderman_elytra": {"texturewidth": 128, "textureheight": 128, "visible_bounds_width": 3, "visible_bounds_height": 3, "visible_bounds_offset": [0, 1.5, 0], "bones": [{"name": "root", "pivot": [0, 0, 0]}, {"name": "waist", "parent": "root", "pivot": [0, 12, 0]}, {"name": "body", "parent": "waist", "pivot": [0, 24, 0]}, {"name": "cape", "parent": "body", "pivot": [0, 24, 3]}, {"name": "leftArm", "parent": "body", "pivot": [5, 22, 0], "cubes": [{"origin": [-4, 16, 0], "size": [8, 8, 1], "uv": [0, 17], "inflate": 0.05}]}, {"name": "rightArm", "parent": "body", "pivot": [-5, 22, 0], "cubes": [{"origin": [-4, 16, 0], "size": [8, 8, 1], "uv": [23, 17], "inflate": 0.05}]}]}}