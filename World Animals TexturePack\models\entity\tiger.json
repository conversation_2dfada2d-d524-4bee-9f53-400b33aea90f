{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.tiger", "texture_width": 128, "texture_height": 128, "visible_bounds_width": 4, "visible_bounds_height": 3, "visible_bounds_offset": [0, 0.5, 0]}, "bones": [{"name": "bone", "pivot": [0, 13, 0], "cubes": [{"origin": [-5, 8, -9], "size": [10, 4, 18], "uv": [0, 106]}, {"origin": [-4.5, 12, -9], "size": [9, 2, 18], "uv": [74, 100]}]}, {"name": "head", "parent": "bone", "pivot": [0, 12, -7], "cubes": [{"origin": [-3, 9.85, -15], "size": [6, 6, 4], "uv": [36, 12]}, {"origin": [2, 14.85, -12], "size": [2, 2, 1], "uv": [0, 17]}, {"origin": [-4, 14.85, -12], "size": [2, 2, 1], "uv": [0, 17]}, {"origin": [-2, 10, -18], "size": [4, 3, 4], "uv": [0, 31]}, {"origin": [-0.5, 12.1, -18.1], "size": [1, 1, 1], "uv": [120, 94]}, {"origin": [-2, 10, -17], "size": [4, 1, 3], "inflate": -0.1, "pivot": [0, 9, -14], "rotation": [15, 0, 0], "uv": [23, 21]}, {"origin": [-3, 9, -15], "size": [6, 6, 8], "inflate": -0.25, "pivot": [-2, 1, -10], "rotation": [-15, 0, 0], "uv": [36, 30]}]}, {"name": "chest1", "parent": "bone", "pivot": [6, 8, 3], "rotation": [0, -90, 0]}, {"name": "tale", "parent": "bone", "pivot": [0, 12, 9], "cubes": [{"origin": [-0.5, 11.5, 8], "size": [1, 1, 6], "inflate": 0.25, "pivot": [0, 12, 9], "rotation": [-35, 0, 0], "uv": [26, 66]}, {"origin": [-0.5, 9, 13.4], "size": [1, 1, 6], "inflate": 0.25, "pivot": [0, 12, 9], "rotation": [-5, 0, 0], "uv": [26, 66]}]}, {"name": "chest2", "parent": "bone", "pivot": [6, 8, 3], "rotation": [0, -90, 0], "cubes": [{"origin": [0, 6, 2.5], "size": [8, 6, 3], "inflate": 0.45, "pivot": [4, 9, 4], "rotation": [0, 180, 0], "uv": [15, 26]}, {"origin": [0, 6, 14], "size": [8, 6, 3], "inflate": 0.45, "uv": [15, 26]}, {"origin": [0.5, 12.5, 14], "size": [3, 2, 1], "uv": [0, 51]}, {"origin": [0.5, 12.5, 3], "size": [3, 2, 1], "uv": [0, 51]}, {"origin": [0.5, 13.5, 4], "size": [3, 1, 10], "uv": [1, 45]}, {"origin": [4.5, 12.5, 3], "size": [3, 2, 1], "uv": [0, 51]}, {"origin": [4.5, 13.5, 4], "size": [3, 1, 10], "uv": [1, 45]}, {"origin": [4.5, 12.5, 14], "size": [3, 2, 1], "uv": [0, 51]}, {"origin": [-0.5, 13.75, 5], "size": [8, 1, 8], "uv": [32, 47]}, {"origin": [7.5, 13.75, 6], "size": [1, 1, 6], "uv": [50, 58]}, {"origin": [-1.5, 13.75, 6], "size": [1, 1, 6], "uv": [35, 58]}]}, {"name": "leg0", "pivot": [-3, 12, 7], "cubes": [{"origin": [-5, 0, 5], "size": [4, 6, 4], "uv": [0, 72]}, {"origin": [-5, 0, 4], "size": [4, 2, 1], "uv": [0, 85]}, {"origin": [-5, 6, 5], "size": [4, 6, 4], "inflate": 0.2, "uv": [0, 58]}]}, {"name": "leg2", "pivot": [3, 12, 7], "cubes": [{"origin": [1, 0, 5], "size": [4, 6, 4], "uv": [0, 72]}, {"origin": [1, 0, 4], "size": [4, 2, 1], "uv": [0, 85]}, {"origin": [1, 6, 5], "size": [4, 6, 4], "inflate": 0.2, "uv": [0, 58]}]}, {"name": "leg3", "pivot": [3, 12, -6], "cubes": [{"origin": [1, 0, -8], "size": [4, 6, 4], "uv": [0, 72]}, {"origin": [1, 0, -9], "size": [4, 2, 1], "uv": [0, 85]}, {"origin": [1, 6, -8], "size": [4, 6, 4], "inflate": 0.2, "uv": [0, 58]}]}, {"name": "leg1", "pivot": [-3, 12, -6], "cubes": [{"origin": [-5, 0, -8], "size": [4, 6, 4], "uv": [0, 72]}, {"origin": [-5, 0, -9], "size": [4, 2, 1], "uv": [0, 85]}, {"origin": [-5, 6, -8], "size": [4, 6, 4], "inflate": 0.2, "uv": [0, 58]}]}]}]}