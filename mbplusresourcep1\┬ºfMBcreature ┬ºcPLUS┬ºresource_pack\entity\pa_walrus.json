{"format_version": "1.8.0", "minecraft:client_entity": {"description": {"identifier": "pa:walrus", "materials": {"default": "axolotl", "limbs": "axolotl_limbs"}, "textures": {"blue": "textures/entity/pamobile/pa_walrus", "cyan": "textures/entity/pamobile/pa_walrus", "gold": "textures/entity/pamobile/pa_walrus", "lucy": "textures/entity/pamobile/pa_walrus", "wild": "textures/entity/pamobile/pa_walrus"}, "geometry": {"default": "geometry.pa_walrus"}, "animations": {"idle_float": "animation.pa_walrus.idle_underwater", "idle_floor": "animation.pa_walrus.look_at_target", "idle_floor_water": "animation.pa_walrus.idle_floor_underwater", "swim": "animation.pa_walrus.swim", "walk_floor": "animation.pa_walrus.walk_floor", "walk_floor_water": "animation.pa_walrus.walk_floor_underwater", "play_dead": "animation.pa_walrus.play_dead", "swim_angle": "animation.pa_walrus.swim_angle", "look_at_target": "animation.pa_walrus.look_at_target"}, "scripts": {"pre_animation": ["variable.moving = query.ground_speed > 0 || query.vertical_speed > 0;", "variable.pitch = query.body_x_rotation;"]}, "animation_controllers": [{"general": "controller.animation.axolotl.general"}, {"move": "controller.animation.axolotl.move"}], "render_controllers": ["controller.render.axolotl"], "spawn_egg": {"texture": "pa:walrus", "texture_index": 0}}}}