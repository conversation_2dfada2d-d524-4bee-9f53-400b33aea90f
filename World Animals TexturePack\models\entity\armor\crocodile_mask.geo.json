{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.crocodile_mask", "texture_width": 128, "texture_height": 128, "visible_bounds_width": 3, "visible_bounds_height": 4.5, "visible_bounds_offset": [0, 1.75, 0]}, "bones": [{"name": "head", "pivot": [0, 24, 0]}, {"name": "crocodrile_0", "parent": "head", "pivot": [0, 23, -3]}, {"name": "crocodrile_1", "parent": "crocodrile_0", "pivot": [0, 25, 2], "cubes": [{"origin": [-3.5, 26, -4], "size": [7, 2, 6], "uv": [22, 68]}, {"origin": [-3, 27.25, -3.7], "size": [6, 1, 5], "inflate": 0.475, "uv": [72, 19]}, {"origin": [-3, 26.7, -4], "size": [6, 1, 5], "inflate": 0.45, "uv": [72, 19]}, {"origin": [-4, 23, -4], "size": [8, 3, 6], "uv": [60, 1]}]}, {"name": "crocodrile_2", "parent": "crocodrile_1", "pivot": [0, 24, -2], "cubes": [{"origin": [2, 23.1, -7], "size": [1, 1, 5], "inflate": 0.15, "uv": [47, 83]}, {"origin": [-3, 22.8, -10], "size": [6, 1, 12], "inflate": 0.2, "uv": [8, 91]}, {"origin": [-3, 22.4, -10], "size": [6, 1, 12], "inflate": 0.1, "uv": [8, 91]}, {"origin": [-3, 23.1, -7], "size": [1, 1, 5], "inflate": 0.15, "uv": [34, 6]}, {"origin": [-2, 23, -18], "size": [4, 1, 8], "uv": [0, 70]}, {"origin": [-3, 23.9, -10], "size": [6, 1, 8], "inflate": -0.1, "uv": [100, 0]}, {"origin": [-2, 23.9, -18], "size": [4, 1, 8], "inflate": -0.1, "uv": [90, 9]}]}, {"name": "crocodrile_3", "parent": "crocodrile_1", "pivot": [0, 24, -3], "cubes": [{"origin": [-2, 23.9, -20.1], "size": [4, 2, 8], "inflate": 0.1, "uv": [68, 36]}, {"origin": [-2, 26.1, -20.1], "size": [4, 1, 2], "inflate": 0.1, "uv": [0, 7]}, {"origin": [-2, 26.1, -18], "size": [4, 1, 4], "inflate": 0.075, "pivot": [0, 27, -18], "rotation": [-16.75, 0, 0], "uv": [84, 37]}, {"origin": [1, 25.3, -14.2], "size": [1, 1, 3], "inflate": -0.1, "pivot": [0, 25.9, -12], "rotation": [7.5, 0, 0], "uv": [40, 88]}, {"origin": [-2, 25.3, -14.2], "size": [1, 1, 3], "inflate": -0.1, "pivot": [0, 25.9, -12], "rotation": [7.5, 0, 0], "uv": [32, 88]}, {"origin": [-2, 25.3, -10.2], "size": [1, 1, 3], "inflate": -0.1, "pivot": [0, 25.9, -12], "rotation": [7.5, 0, 0], "uv": [34, 76]}, {"origin": [1, 25.3, -10.2], "size": [1, 1, 3], "inflate": -0.1, "pivot": [0, 25.9, -12], "rotation": [7.5, 0, 0], "uv": [38, 47]}, {"origin": [-3, 24.1, -11.9], "size": [6, 2, 9], "inflate": -0.1, "pivot": [0, 25.9, -12], "rotation": [7.5, 0, 0], "uv": [58, 52]}, {"origin": [-3, 24, -12], "size": [6, 2, 8], "uv": [50, 63]}, {"origin": [2, 23.5, -9], "size": [1, 1, 5], "inflate": 0.25, "uv": [0, 19]}, {"origin": [-3, 23.5, -9], "size": [1, 1, 5], "inflate": 0.25, "uv": [0, 0]}, {"origin": [-2, 22.9, -20], "size": [4, 1, 8], "inflate": -0.1, "uv": [90, 9]}, {"origin": [-3, 23.05, -12], "size": [6, 1, 3], "inflate": -0.05, "uv": [0, 93]}, {"origin": [-3, 22.45, -9], "size": [6, 1, 5], "inflate": 0.05, "uv": [96, 20]}]}]}]}