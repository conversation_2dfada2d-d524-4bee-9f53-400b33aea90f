{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.ironman_glider", "texture_width": 64, "texture_height": 64, "visible_bounds_width": 4, "visible_bounds_height": 3.5, "visible_bounds_offset": [0, 1.25, 0]}, "bones": [{"name": "body", "pivot": [0, 24, 0], "cubes": [{"origin": [-2, 16, 2], "size": [4, 6, 1], "uv": [16, 5]}, {"origin": [0, 19.25, 2.5], "size": [1, 2, 1], "inflate": -0.475, "uv": [24, 0]}, {"origin": [-1, 19.25, 2.5], "size": [1, 2, 1], "inflate": -0.475, "uv": [24, 0]}, {"origin": [-0.9, 19.25, 2.5], "size": [1, 2, 1], "inflate": -0.475, "uv": [24, 0]}, {"origin": [-0.1, 19.25, 2.5], "size": [1, 2, 1], "inflate": -0.475, "uv": [24, 0]}, {"origin": [-0.05, 19.25, 2.5], "size": [1, 1, 1], "inflate": -0.475, "uv": [24, 0]}, {"origin": [-0.95, 19.25, 2.5], "size": [1, 1, 1], "inflate": -0.475, "uv": [24, 0]}, {"origin": [-0.05, 20.25, 2.5], "size": [1, 1, 1], "inflate": -0.475, "uv": [24, 0]}, {"origin": [-0.95, 20.25, 2.5], "size": [1, 1, 1], "inflate": -0.475, "uv": [24, 0]}, {"origin": [-2, 21, 2], "size": [4, 1, 1], "inflate": 0.1, "uv": [54, 0]}, {"origin": [0.625, 18.425, 2.3], "size": [1, 3, 1], "inflate": -0.25, "pivot": [1, 19, 3], "rotation": [0, 0, 20], "uv": [52, 2]}, {"origin": [0.75, 18.425, 2.3], "size": [1, 3, 1], "inflate": -0.275, "pivot": [1, 19, 3], "rotation": [0, 0, 20], "uv": [52, 2]}, {"origin": [-1.625, 18.425, 2.3], "size": [1, 3, 1], "inflate": -0.25, "pivot": [-1, 19, 3], "rotation": [0, 0, -20], "uv": [60, 4]}, {"origin": [-1.75, 18.425, 2.3], "size": [1, 3, 1], "inflate": -0.275, "pivot": [-1, 19, 3], "rotation": [0, 0, -20], "uv": [60, 4]}, {"origin": [-1, 18, 1.825], "size": [2, 1, 1], "inflate": 0.25, "uv": [52, 8]}, {"origin": [0.05, 20.55, 2], "size": [2, 2, 1], "inflate": -0.05, "pivot": [1.05, 22.95, 2.5], "rotation": [0, 0, -45], "uv": [46, 25]}, {"origin": [-2.05, 20.55, 2], "size": [2, 2, 1], "inflate": -0.05, "pivot": [-1.05, 22.95, 2.5], "rotation": [0, 0, 45], "uv": [46, 22]}, {"origin": [2, 19, 2], "size": [2, 5, 1], "uv": [32, 0]}, {"origin": [-4, 19, 2], "size": [2, 5, 1], "uv": [24, 27]}, {"origin": [-4, 24, 2], "size": [2, 2, 1], "inflate": -0.025, "pivot": [0, 24, 3], "rotation": [35, 0, 0], "uv": [38, 30]}, {"origin": [2, 24, 2], "size": [2, 2, 1], "inflate": -0.025, "pivot": [0, 24, 3], "rotation": [35, 0, 0], "uv": [46, 7]}, {"origin": [-4.44, 17.975, 2], "size": [3, 2, 1], "inflate": -0.025, "pivot": [-3, 18, 2.5], "rotation": [0, 0, 45], "uv": [44, 36]}, {"origin": [-4.44, 16.975, 2], "size": [3, 1, 1], "inflate": -0.025, "pivot": [-3, 18, 2.5], "rotation": [0, 0, 45], "uv": [49, 10]}, {"origin": [1.44, 17.975, 2], "size": [3, 2, 1], "inflate": -0.025, "pivot": [3, 18, 2.5], "rotation": [0, 0, -45], "uv": [44, 39]}, {"origin": [1.44, 16.975, 2], "size": [3, 1, 1], "inflate": -0.025, "pivot": [3, 18, 2.5], "rotation": [0, 0, -45], "uv": [49, 13]}, {"origin": [3.84, 18.175, 2], "size": [1, 3, 1], "inflate": -0.125, "uv": [50, 15]}, {"origin": [-4.84, 18.175, 2], "size": [1, 3, 1], "inflate": -0.125, "uv": [58, 9]}]}, {"name": "bone0", "parent": "body", "pivot": [3, 22.3, 3.2], "rotation": [0, 0, 47.5], "cubes": [{"origin": [2, 22.1, 2.7], "size": [2, 3, 1], "inflate": -0.2, "uv": [46, 0]}, {"origin": [2, 23.525, 4.6], "size": [2, 3, 1], "inflate": -0.225, "pivot": [3, 22.3, 3.2], "rotation": [50, 0, 0], "uv": [22, 45]}, {"origin": [2, 25.775, -0.225], "size": [2, 1, 2], "inflate": -0.25, "uv": [32, 44]}, {"origin": [2, 25.775, 1.075], "size": [2, 1, 1], "inflate": -0.25, "uv": [32, 47]}, {"origin": [1.5, 26.075, -0.825], "size": [2, 1, 3], "inflate": -0.45, "pivot": [3, 22.3, 3.2], "rotation": [0, 0, 27.5], "uv": [22, 36]}, {"origin": [2.5, 26.075, -0.825], "size": [2, 1, 3], "inflate": -0.45, "pivot": [3, 22.3, 3.2], "rotation": [0, 0, -27.5], "uv": [32, 22]}, {"origin": [1.5, 26.075, -0.825], "size": [3, 1, 3], "inflate": -0.45, "uv": [16, 14]}, {"origin": [2, 25.775, -0.425], "size": [2, 1, 1], "inflate": -0.3, "uv": [46, 46]}]}, {"name": "bone1", "parent": "body", "pivot": [-3, 22.3, 3.2], "rotation": [0, 0, -45], "cubes": [{"origin": [-4, 22.1, 2.7], "size": [2, 3, 1], "inflate": -0.2, "uv": [0, 46]}, {"origin": [-4.5, 26.075, -0.825], "size": [3, 1, 3], "inflate": -0.45, "uv": [0, 18]}, {"origin": [-4, 23.525, 4.6], "size": [2, 3, 1], "inflate": -0.225, "pivot": [-3, 22.3, 3.2], "rotation": [50, 0, 0], "uv": [40, 44]}, {"origin": [-3.5, 26.075, -0.825], "size": [2, 1, 3], "inflate": -0.45, "pivot": [-3, 22.3, 3.2], "rotation": [0, 0, -27.5], "uv": [8, 36]}, {"origin": [-4.5, 26.075, -0.825], "size": [2, 1, 3], "inflate": -0.45, "pivot": [-3, 22.3, 3.2], "rotation": [0, 0, 27.5], "uv": [39, 8]}, {"origin": [-4, 25.775, -0.425], "size": [2, 1, 1], "inflate": -0.3, "uv": [46, 44]}, {"origin": [-4, 25.775, -0.225], "size": [2, 1, 2], "inflate": -0.25, "uv": [14, 44]}, {"origin": [-4, 25.775, 1.075], "size": [2, 1, 1], "inflate": -0.25, "uv": [14, 47]}]}, {"name": "propulsor0", "parent": "body", "pivot": [2, 23, 3], "cubes": [{"origin": [1, 22, 3], "size": [2, 2, 6], "inflate": 0.1, "uv": [26, 34]}, {"origin": [1, 21, 5], "size": [2, 1, 4], "uv": [16, 0]}, {"origin": [1, 19.8, 6.7], "size": [2, 1, 2], "inflate": 0.1, "uv": [0, 43]}, {"origin": [1, 19.575, 6.7], "size": [2, 1, 2], "inflate": -0.1, "uv": [26, 42]}, {"origin": [1, 20.5, 6.7], "size": [2, 1, 2], "inflate": -0.1, "uv": [42, 17]}, {"origin": [1, 22.5, 3.9], "size": [2, 1, 8], "inflate": -0.03, "uv": [12, 27]}, {"origin": [3, 23, 3], "size": [1, 2, 6], "inflate": -0.4, "uv": [38, 22]}, {"origin": [0, 23, 3], "size": [1, 2, 6], "inflate": -0.4, "uv": [36, 36]}, {"origin": [0, 21.4, 5.9], "size": [1, 3, 2], "inflate": -0.401, "uv": [36, 34]}, {"origin": [3, 21.4, 5.9], "size": [1, 3, 2], "inflate": -0.401, "uv": [8, 43]}, {"origin": [0, 22.4, 3.7], "size": [1, 2, 3], "inflate": -0.401, "uv": [41, 31]}, {"origin": [3, 22.4, 3.7], "size": [1, 2, 3], "inflate": -0.401, "uv": [42, 12]}, {"origin": [0, 24, 3], "size": [4, 1, 6], "inflate": -0.401, "uv": [32, 0]}, {"origin": [1, 23, 9], "size": [2, 1, 12], "uv": [16, 14]}, {"origin": [1, 22.4, 9], "size": [2, 1, 12], "inflate": -0.025, "pivot": [2, 22, 15], "rotation": [5.5, 0, 0], "uv": [16, 1]}, {"origin": [1, 22.1, 9], "size": [2, 1, 3], "inflate": -0.225, "pivot": [2, 22, 15], "rotation": [5.5, 0, 0], "uv": [32, 7]}, {"origin": [1, 22.1, 12], "size": [2, 1, 1], "inflate": -0.3, "pivot": [2, 22, 15], "rotation": [5.5, 0, 0], "uv": [46, 32]}, {"origin": [1, 22.1, 13], "size": [2, 1, 1], "inflate": -0.3, "pivot": [2, 22, 15], "rotation": [5.5, 0, 0], "uv": [46, 4]}, {"origin": [1, 22.1, 15], "size": [2, 1, 1], "inflate": -0.3, "pivot": [2, 22, 15], "rotation": [5.5, 0, 0], "uv": [22, 22]}, {"origin": [1, 22.1, 14], "size": [2, 1, 1], "inflate": -0.3, "pivot": [2, 22, 15], "rotation": [5.5, 0, 0], "uv": [12, 32]}, {"origin": [1, 22.1, 16], "size": [2, 1, 1], "inflate": -0.3, "pivot": [2, 22, 15], "rotation": [5.5, 0, 0], "uv": [0, 10]}, {"origin": [1, 22.1, 17], "size": [2, 1, 4], "inflate": -0.3, "pivot": [2, 22, 15], "rotation": [5.5, 0, 0], "uv": [0, 5]}]}, {"name": "propulsor1", "parent": "body", "pivot": [-2, 23, 3], "cubes": [{"origin": [-3, 22, 3], "size": [2, 2, 6], "inflate": 0.1, "uv": [32, 14]}, {"origin": [-3, 21, 5], "size": [2, 1, 4], "uv": [0, 13]}, {"origin": [-3, 19.8, 6.7], "size": [2, 1, 2], "inflate": 0.1, "uv": [0, 31]}, {"origin": [-3, 19.575, 6.7], "size": [2, 1, 2], "inflate": -0.1, "uv": [16, 22]}, {"origin": [-3, 20.5, 6.7], "size": [2, 1, 2], "inflate": -0.1, "uv": [0, 22]}, {"origin": [-3, 22.5, 3.9], "size": [2, 1, 8], "inflate": -0.03, "uv": [0, 26]}, {"origin": [-1, 23, 3], "size": [1, 2, 6], "inflate": -0.4, "uv": [14, 36]}, {"origin": [-4, 23, 3], "size": [1, 2, 6], "inflate": -0.4, "uv": [0, 35]}, {"origin": [-4, 21.4, 5.9], "size": [1, 3, 2], "inflate": -0.401, "uv": [0, 35]}, {"origin": [-1, 21.4, 5.9], "size": [1, 3, 2], "inflate": -0.401, "uv": [32, 14]}, {"origin": [-4, 22.4, 3.7], "size": [1, 2, 3], "inflate": -0.401, "uv": [12, 27]}, {"origin": [-1, 22.4, 3.7], "size": [1, 2, 3], "inflate": -0.401, "uv": [0, 26]}, {"origin": [-4, 24, 3], "size": [4, 1, 6], "inflate": -0.401, "uv": [24, 27]}, {"origin": [-3, 23, 9], "size": [2, 1, 12], "uv": [0, 13]}, {"origin": [-3, 22.4, 9], "size": [2, 1, 12], "inflate": -0.025, "pivot": [-2, 22, 15], "rotation": [5.5, 0, 0], "uv": [0, 0]}, {"origin": [-3, 22.1, 9], "size": [2, 1, 3], "inflate": -0.225, "pivot": [-2, 22, 15], "rotation": [5.5, 0, 0], "uv": [16, 18]}, {"origin": [-3, 22.1, 12], "size": [2, 1, 1], "inflate": -0.3, "pivot": [-2, 22, 15], "rotation": [5.5, 0, 0], "uv": [46, 30]}, {"origin": [-3, 22.1, 13], "size": [2, 1, 1], "inflate": -0.3, "pivot": [-2, 22, 15], "rotation": [5.5, 0, 0], "uv": [14, 40]}, {"origin": [-3, 22.1, 15], "size": [2, 1, 1], "inflate": -0.3, "pivot": [-2, 22, 15], "rotation": [5.5, 0, 0], "uv": [6, 22]}, {"origin": [-3, 22.1, 14], "size": [2, 1, 1], "inflate": -0.3, "pivot": [-2, 22, 15], "rotation": [5.5, 0, 0], "uv": [32, 11]}, {"origin": [-3, 22.1, 16], "size": [2, 1, 1], "inflate": -0.3, "pivot": [-2, 22, 15], "rotation": [5.5, 0, 0], "uv": [6, 10]}, {"origin": [-3, 22.1, 17], "size": [2, 1, 4], "inflate": -0.3, "pivot": [-2, 22, 15], "rotation": [5.5, 0, 0], "uv": [0, 0]}]}]}]}