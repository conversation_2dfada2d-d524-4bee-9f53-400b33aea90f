{
  "format_version": "1.13.0",
  "minecraft:entity": {
    "description": {
      "identifier": "worldanimals:zebra",
      "is_spawnable": true,
      "is_summonable": true,
      "is_experimental": false
    },

    "component_groups": {
      "minecraft:zebra_baby": {
        "minecraft:is_baby": {
        },
        "minecraft:scale": {
          "value": 0.5
        },
        "minecraft:ageable": {
          "duration": 1200,
          "feed_items": [
            {
              "item": "wheat",
              "growth": 0.016667
            },
            {
              "item": "sugar",
              "growth": 0.025
            },
            {
              "item": "hay_block",
              "growth": 0.15
            },
            {
              "item": "apple",
              "growth": 0.05
            },
            {
              "item": "golden_carrot",
              "growth": 0.05
            },
            {
              "item": "golden_apple",
              "growth": 0.2
            },
            {
              "item": "appleEnchanted",
              "growth": 0.2
            }
          ],
          "grow_up": {
            "event": "minecraft:ageable_grow_up",
            "target": "self"
          }
        },

        "minecraft:behavior.follow_parent": {
          "priority": 5,
          "speed_multiplier": 1.0
        }
      },

      "minecraft:zebra_adult": {
        "minecraft:scale": {
          "value": 1.0
        },
        "minecraft:experience_reward": {
          "on_bred": "Math.Random(1,7)",
          "on_death": "query.last_hit_by_player ? Math.Random(1,3) : 0"
        },
        "minecraft:collision_box": {
          "width": 0.9,
          "height": 1.87
        },
        "minecraft:behavior.breed": {
          "priority": 4,
          "speed_multiplier": 1.0
        },
        "minecraft:breedable": {
          "require_tame": true,
          "inherit_tamed": false,
          "love_filters": { "test": "is_mark_variant", "subject": "self", "operator": "!=", "value": 1 }, // Wandering Trader zebras can't fall in love
          "breeds_with": {
            "mate_type": "worldanimals:zebra",
            "baby_type": "worldanimals:zebra",
            "breed_event": {
              "event": "minecraft:entity_born",
              "target": "baby"
            }
          },
          "breed_items": [ "hay_block" ]
        }
      },

      "minecraft:zebra_wandering_trader": {
        "minecraft:mark_variant": {
          "value": 1
        },
        "minecraft:on_friendly_anger": {
          "event": "minecraft:defend_wandering_trader",
          "target": "self"
        }, 
        "minecraft:environment_sensor": {
          // If this is a Wandering Trader's zebra and it was just released from its leash, make it tame
          "triggers": {
            "filters": {
              "all_of": [
                { "test": "is_leashed", "subject": "self", "value": false },
                { "test": "has_component", "subject": "self", "operator": "!=", "value" :  "minecraft:is_tamed" }
              ]
            },
            "event": "minecraft:on_tame"
          }
        }
      },

      "minecraft:strength_1": {
        "minecraft:strength": {
          "value": 1,
          "max": 5
        }
      },
      "minecraft:strength_2": {
        "minecraft:strength": {
          "value": 2,
          "max": 5
        }
      },
      "minecraft:strength_3": {
        "minecraft:strength": {
          "value": 3,
          "max": 5
        }
      },
      "minecraft:strength_4": {
        "minecraft:strength": {
          "value": 4,
          "max": 5
        }
      },
      "minecraft:strength_5": {
        "minecraft:strength": {
          "value": 5,
          "max": 5
        }
      },

      "minecraft:zebra_creamy": {
        "minecraft:variant": {
          "value": 0
        }
      },
      "minecraft:zebra_white": {
        "minecraft:variant": {
          "value": 1
        }
      },
      "minecraft:zebra_brown": {
        "minecraft:variant": {
          "value": 2
        }
      },
      "minecraft:zebra_gray": {
        "minecraft:variant": {
          "value": 3
        }
      },

      "minecraft:zebra_wild": {
      },


      "minecraft:zebra_tamed": {
        "minecraft:behavior.owner_hurt_by_target": {
          "priority": 1
        },
        "minecraft:behavior.owner_hurt_target": {
          "priority": 2
        },
        "minecraft:is_tamed": {
        }
      },
      "minecraft:zebra_unchested": {
	  
      },

      "minecraft:zebra_chested": {
	  "minecraft:inventory": {
        "inventory_size": 27,
        "container_type": "container", 
        "private": false
      },
        "minecraft:input_ground_controlled": {
        },
        "minecraft:can_power_jump": {
        },
        "minecraft:is_chested": {

        },
        "minecraft:loot": {
          "table": "loot_tables/entities/diamond_zebra_saddle.json"
        }
      },

      "minecraft:zebra_angry": {
        "minecraft:angry": {
          "duration": 4,
          "broadcast_anger": false,
          "calm_event": {
            "event": "minecraft:on_calm",
            "target": "self"
          }
        }
      },
      "minecraft:zebra_angry_wolf": {
        "minecraft:angry": {
          "duration": 25,
          "broadcast_anger": true,
          "broadcast_range": 20,
          "calm_event": {
            "event": "minecraft:on_calm",
            "target": "self"
          }
        },
        "minecraft:on_target_acquired": {
        }
      },
      "minecraft:zebra_defend_trader": {
        "minecraft:angry": {
          "duration": 10,
          "calm_event": {
            "event": "minecraft:on_calm",
            "target": "self"
          }
        }
      },

      "minecraft:in_caravan": {
        "minecraft:damage_sensor": {
          "triggers": {
            "cause": "all",
            "deals_damage": true
          }
        }
      }

    },


    "components": {
      "minecraft:type_family": {
        "family": [ "zebra", "mob" ]
      },
      "minecraft:breathable": {
        "total_supply": 15,
        "suffocate_time": 0
      },
      "minecraft:nameable": {
      },
      "minecraft:mark_variant": {
        "value": 0
      },
      "minecraft:health": {
        "value": {
          "range_min": 25,
          "range_max": 25
        }
      },
        "minecraft:loot": {
          "table": "loot_tables/entities/zebra.json"
        },
      "minecraft:hurt_on_condition": {
        "damage_conditions": [
          {
            "filters": { "test": "in_lava", "subject": "self", "operator": "==", "value": true },
            "cause": "lava",
            "damage_per_tick": 4
          }
        ]
      },
      "minecraft:movement": {
        "value": 0.35
      },
      "minecraft:navigation.walk": {
        "can_path_over_water": true,
        "avoid_damage_blocks": true
      },
      "minecraft:movement.basic": {
      },
      "minecraft:jump.static": {
      },
      "minecraft:follow_range": {
        "value": 40,
        "max": 40
      },
      "minecraft:leashable": {
        "soft_distance": 4.0,
        "hard_distance": 6.0,
        "max_distance": 10.0,
        "can_be_stolen": true
      },
      "minecraft:balloonable": {
      },
      "minecraft:healable": {
        "items": [
          {
            "item": "wheat",
            "heal_amount": 2
          },
          {
            "item": "sugar",
            "heal_amount": 1
          },
          {
            "item": "hay_block",
            "heal_amount": 20
          },
          {
            "item": "apple",
            "heal_amount": 3
          },
          {
            "item": "golden_carrot",
            "heal_amount": 4
          },
          {
            "item": "golden_apple",
            "heal_amount": 10
          },
          {
            "item": "appleEnchanted",
            "heal_amount": 10
          }
        ]
      },

      "minecraft:behavior.float": {
        "priority": 0
      },
      "minecraft:behavior.run_around_like_crazy": {
        "priority": 1,
        "speed_multiplier": 1.2
      },
      "minecraft:behavior.random_stroll": {
        "priority": 6,
        "speed_multiplier": 0.7
      },
      "minecraft:behavior.look_at_player": {
        "priority": 7,
        "look_distance": 6.0,
        "probability": 0.02
      },
      "minecraft:behavior.random_look_around": {
        "priority": 8
      },
      "minecraft:behavior.mount_pathing": {
        "priority": 1,
        "speed_multiplier": 1.25,
        "target_dist": 0.0,
        "track_target": true
      },
      "minecraft:behavior.hurt_by_target": {
        "priority": 1,
        "hurt_owner": true
      },
      "minecraft:damage_sensor": {
        "triggers": {
          "cause": "all",
          "deals_damage": true,
          "on_damage": {
            "filters": { "test": "in_caravan", "value": false },
            "event": "minecraft:become_angry"
          }
        }
      },
      "minecraft:on_target_acquired": {
        "filters": {
                "all_of": [
                  { "test" :  "is_family", "subject" : "target", "value" :  "wolf"},
                  { "test" :  "has_component","subject" : "target", "operator": "!=", "value" :  "minecraft:is_tamed"}
                ]
              },
        "event": "minecraft:mad_at_wolf",
        "target": "self"
      },
      "minecraft:on_target_escape": {
        "filters": {
                "all_of": [
                  { "test" :  "is_family", "subject" : "target", "value" :  "wolf"},
                  { "test" :  "has_component","subject" : "target", "operator": "!=", "value" :  "minecraft:is_tamed"}
                ]
              },
        "event": "minecraft:on_calm",
        "target": "self"
      },
	    "minecraft:physics": {
      },
      "minecraft:pushable": {
        "is_pushable": true,
        "is_pushable_by_piston": true
      }
    },


    "events": {
      "minecraft:entity_spawned": {
        "sequence": [
          {
            "randomize": [
              {
                "weight": 90,
                "remove": {
                },
                "add": {
                  "component_groups": [
                    "minecraft:zebra_adult",
                    "minecraft:zebra_wild"
                  ]
                }
              },
              {
                "weight": 10,
                "remove": {
                },
                "add": {
                  "component_groups": [
                    "minecraft:zebra_baby"
                  ]

                }
              }
            ]
          },
          {
            "randomize": [
              {
                "weight": 32,
                "add": {
                  "component_groups": [
                    "minecraft:strength_1"
                  ]
                }
              },
              {
                "weight": 32,
                "add": {
                  "component_groups": [
                    "minecraft:strength_2"
                  ]
                }
              },
              {
                "weight": 32,
                "add": {
                  "component_groups": [
                    "minecraft:strength_3"
                  ]
                }
              },
              {
                "weight": 2,
                "add": {
                  "component_groups": [
                    "minecraft:strength_4"
                  ]
                }
              },
              {
                "weight": 2,
                "add": {
                  "component_groups": [
                    "minecraft:strength_5"
                  ]
                }
              }
            ]
          },
          {
            "randomize": [
              {
                "weight": 25,
                "add": {
                  "component_groups": [
                    "minecraft:zebra_creamy"
                  ]
                }
              },
              {
                "weight": 25,
                "add": {
                  "component_groups": [
                    "minecraft:zebra_white"
                  ]
                }
              },
              {
                "weight": 25,
                "add": {
                  "component_groups": [
                    "minecraft:zebra_brown"
                  ]
                }
              },
              {
                "weight": 25,
                "add": {
                  "component_groups": [
                    "minecraft:zebra_gray"
                  ]
                }
              }
            ]
          }
        ]
      },

      "minecraft:entity_born": {
        "add": {
          "component_groups": [
            "minecraft:zebra_baby"
          ]
        }
      },

      "minecraft:from_wandering_trader": {
        "sequence": [
          {
            "add": {
              "component_groups": [
                "minecraft:zebra_adult",
                "minecraft:zebra_wandering_trader"
              ]
            }
          },
          {
            "randomize": [
              {
                "weight": 32,
                "add": {
                  "component_groups": [
                    "minecraft:strength_1"
                  ]
                }
              },
              {
                "weight": 32,
                "add": {
                  "component_groups": [
                    "minecraft:strength_2"
                  ]
                }
              },
              {
                "weight": 32,
                "add": {
                  "component_groups": [
                    "minecraft:strength_3"
                  ]
                }
              },
              {
                "weight": 2,
                "add": {
                  "component_groups": [
                    "minecraft:strength_4"
                  ]
                }
              },
              {
                "weight": 2,
                "add": {
                  "component_groups": [
                    "minecraft:strength_5"
                  ]
                }
              }
            ]
          },
          {
            "randomize": [
              {
                "weight": 25,
                "add": {
                  "component_groups": [
                    "minecraft:zebra_creamy"
                  ]
                }
              },
              {
                "weight": 25,
                "add": {
                  "component_groups": [
                    "minecraft:zebra_white"
                  ]
                }
              },
              {
                "weight": 25,
                "add": {
                  "component_groups": [
                    "minecraft:zebra_brown"
                  ]
                }
              },
              {
                "weight": 25,
                "add": {
                  "component_groups": [
                    "minecraft:zebra_gray"
                  ]
                }
              }
            ]
          }
        ]
      },

      "minecraft:ageable_grow_up": {
        "remove": {
          "component_groups": [
            "minecraft:zebra_baby"
          ]
        },
        "add": {
          "component_groups": [
            "minecraft:zebra_adult",
            "minecraft:zebra_wild"
          ]
        }
      },

      "minecraft:on_tame": {
        "remove": {
          "component_groups": [
            "minecraft:zebra_wild"
          ]
        },
        "add": {
          "component_groups": [
            "minecraft:zebra_tamed",
            "minecraft:zebra_unchested"
          ]
        }
      },
      "minecraft:join_caravan": {
        "add": {
          "component_groups": [
            "minecraft:in_caravan"
          ]
        }
      },
      "minecraft:leave_caravan": {
        "remove": {
          "component_groups": [
            "minecraft:in_caravan"
          ]
        }
      },
      "minecraft:mad_at_wolf": {
        "add": {
          "component_groups": [
            "minecraft:zebra_angry_wolf"
          ]
        }
      },
      "minecraft:defend_wandering_trader": {
        "add": {
          "component_groups": [
            "minecraft:zebra_defend_trader"
          ]
        }
      },
      "minecraft:become_angry": {
        "add": {
          "component_groups": [
            "minecraft:zebra_angry"
          ]
        }
      },
      "minecraft:on_calm": {
        "remove": {
          "component_groups": [
            "minecraft:zebra_angry",
            "minecraft:zebra_angry_wolf",
            "minecraft:zebra_defend_trader"
          ]
        }
      },

      "minecraft:on_chest": {
        "remove": {
          "component_groups": [
            "minecraft:zebra_unchested"
          ]
        },
        "add": {
          "component_groups": [
            "minecraft:zebra_chested"
          ]
        }
      }

    }
  }
}
