{"format_version": "1.13.0", "minecraft:entity": {"description": {"identifier": "worldanimals:flamingo", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"minecraft:flamingo_baby": {"minecraft:is_baby": {}, "minecraft:scale": {"value": 0.5}, "minecraft:ageable": {"duration": 1200, "feed_items": ["wheat_seeds", "beetroot_seeds", "melon_seeds", "pumpkin_seeds"], "grow_up": {"event": "minecraft:ageable_grow_up", "target": "self"}}, "minecraft:behavior.follow_parent": {"priority": 5, "speed_multiplier": 1.1}}, "minecraft:flamingo_adult": {"minecraft:experience_reward": {"on_bred": "<PERSON><PERSON>(1,7)", "on_death": "query.last_hit_by_player ? Math.Random(1,3) : 0"}, "minecraft:loot": {"table": "loot_tables/entities/plumas.json"}, "minecraft:breedable": {"require_tame": false, "breeds_with": {"mate_type": "worldanimals:flamingo", "baby_type": "worldanimals:flamingo", "breed_event": {"event": "minecraft:entity_born", "target": "baby"}}, "breed_items": ["wheat_seeds", "beetroot_seeds", "melon_seeds", "pumpkin_seeds"]}, "minecraft:behavior.breed": {"priority": 3, "speed_multiplier": 1.0}, "minecraft:rideable": {"seat_count": 1, "family_types": ["zombie"], "seats": {"position": [0.0, 0.4, 0.0]}}, "minecraft:spawn_entity": {"min_wait_time": 200, "max_wait_time": 450, "spawn_sound": "plop", "spawn_item": "feather", "filters": {"test": "rider_count", "subject": "self", "operator": "==", "value": 0}}}}, "components": {"minecraft:type_family": {"family": ["flamingo", "mob"]}, "minecraft:breathable": {"total_supply": 15, "suffocate_time": 0}, "minecraft:collision_box": {"width": 0.6, "height": 0.8}, "minecraft:nameable": {}, "minecraft:health": {"value": 4, "max": 4}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:movement": {"value": 0.25}, "minecraft:damage_sensor": {"triggers": {"cause": "fall", "deals_damage": false}}, "minecraft:leashable": {"soft_distance": 4.0, "hard_distance": 6.0, "max_distance": 10.0}, "minecraft:balloonable": {"mass": 0.6}, "minecraft:navigation.walk": {"can_path_over_water": true, "avoid_damage_blocks": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.panic": {"priority": 1, "speed_multiplier": 1.5}, "minecraft:behavior.mount_pathing": {"priority": 2, "speed_multiplier": 1.5, "target_dist": 0.0, "track_target": true}, "minecraft:behavior.tempt": {"priority": 4, "speed_multiplier": 1.0, "items": ["wheat_seeds", "beetroot_seeds", "melon_seeds", "pumpkin_seeds"]}, "minecraft:behavior.random_stroll": {"priority": 6, "speed_multiplier": 1.0}, "minecraft:behavior.look_at_player": {"priority": 7, "look_distance": 6.0, "probability": 0.02}, "minecraft:behavior.random_look_around": {"priority": 8}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}}, "events": {"from_egg": {"add": {"component_groups": ["minecraft:flamingo_baby"]}}, "minecraft:entity_spawned": {"randomize": [{"weight": 95, "remove": {}, "add": {"component_groups": ["minecraft:flamingo_adult"]}}, {"weight": 5, "remove": {}, "add": {"component_groups": ["minecraft:flamingo_baby"]}}]}, "minecraft:entity_born": {"remove": {}, "add": {"component_groups": ["minecraft:flamingo_baby"]}}, "minecraft:ageable_grow_up": {"remove": {"component_groups": ["minecraft:flamingo_baby"]}, "add": {"component_groups": ["minecraft:flamingo_adult"]}}}}}