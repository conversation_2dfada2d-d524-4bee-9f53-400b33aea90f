{"format_version": "1.8.0", "minecraft:entity": {"description": {"identifier": "pa:elephant", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"minecraft:cow_baby": {"minecraft:is_baby": {}, "minecraft:scale": {"value": 0.5}, "minecraft:ageable": {"duration": 1200, "feedItems": "wheat", "grow_up": {"event": "minecraft:ageable_grow_up", "target": "self"}}, "minecraft:behavior.follow_parent": {"priority": 6, "speed_multiplier": 1.1}}, "minecraft:cow_adult": {"minecraft:behavior.breed": {"priority": 3, "speed_multiplier": 1.0}, "minecraft:breedable": {"requireTame": false, "breedItems": "wheat", "breedsWith": {"mateType": "minecraft:cow", "babyType": "minecraft:cow", "breed_event": {"event": "minecraft:entity_born", "target": "baby"}}}, "minecraft:interact": [{"on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_equipment", "domain": "hand", "subject": "other", "value": "bucket:0"}]}}, "use_item": true, "transform_to_item": "bucket:1", "play_sounds": "milk", "interact_text": "action.interact.milk"}]}}, "components": {"minecraft:type_family": {"family": ["irongolem", "ravager"]}, "minecraft:breathable": {"totalSupply": 15, "suffocateTime": 0}, "minecraft:navigation.walk": {"can_path_over_water": true, "avoid_water": true, "avoid_damage_blocks": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:collision_box": {"height": 1.1, "width": 1.2}, "minecraft:health": {"value": 60, "max": 60}, "minecraft:movement": {"value": 0.37}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.mount_pathing": {"priority": 2, "speed_multiplier": 1.5, "target_dist": 0.0, "track_target": true}, "minecraft:behavior.breed": {"priority": 3, "speed_multiplier": 1.0}, "minecraft:behavior.tempt": {"priority": 4, "speed_multiplier": 1.25, "items": ["wheat"]}, "minecraft:behavior.follow_parent": {"priority": 5, "speed_multiplier": 1.1}, "minecraft:behavior.random_stroll": {"priority": 6, "speed_multiplier": 0.8}, "minecraft:behavior.look_at_player": {"priority": 7, "look_distance": 6.0, "probability": 0.02}, "minecraft:behavior.random_look_around": {"priority": 9}, "minecraft:leashable": {"soft_distance": 4.0, "hard_distance": 6.0, "max_distance": 10.0}, "minecraft:balloonable": {}, "minecraft:physics": {}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 10 : 0"}, "minecraft:underwater_movement": {"value": 0.02}, "minecraft:scale": {"value": 2}, "minecraft:attack": {"damage": 10}, "minecraft:behavior.melee_attack": {"priority": 2, "speed_multiplier": 1.5, "target_dist": 0.0, "max_dist": 3, "random_stop_interval": 100, "track_target": false, "reach_multiplier": 1.4}, "minecraft:behavior.hurt_by_target": {"priority": 3}, "minecraft:behavior.owner_hurt_by_target": {"priority": 1}, "minecraft:behavior.owner_hurt_target": {"priority": 2}, "minecraft:on_death": {"event": "on:death", "target": "self"}, "minecraft:on_hurt": {"event": "on:hurt", "target": "self"}, "minecraft:on_hurt_by_player": {"event": "on:hurt_by_player", "target": "self"}, "minecraft:on_ignite": {"event": "on:ignite", "target": "self"}, "minecraft:on_target_acquired": {"event": "on:target_acquired", "target": "self"}, "minecraft:on_target_escape": {"event": "on:target_escape", "target": "self"}, "minecraft:on_wake_with_owner": {"event": "on:wake_with_owner", "target": "self"}, "minecraft:behavior.avoid_mob_type": {"priority": 3, "entity_types": [{"filters": {"other_with_families": ["monster", "ocelot", "polarbear"]}, "max_dist": 6, "walk_speed_multiplier": 1.0, "sprint_speed_multiplier": 1.2}], "max_dist": 6, "walk_speed_multiplier": 1.0, "sprint_speed_multiplier": 1.2, "sneak_speed_multiplier": 1.0}, "minecraft:knockback_resistance": {"value": 10.0, "max": 10.0}, "minecraft:break_blocks": {"breakable_blocks": ["azalea_leaves", "azalea_leaves_flowered", "cobblestone", "monster_egg", "leaves", "leaves2"]}, "minecraft:behavior.panic": {"speed_multiplier": 1.25}, "minecraft:annotation.break_door": {"break_time": 0.1, "min_difficulty": "hard"}, "minecraft:loot": {"table": "loot_tables/entities/pa_elephant.json"}}, "events": {"minecraft:entity_spawned": {"randomize": [{"weight": 95, "add": {"component_groups": ["minecraft:cow_adult"]}}, {"weight": 5, "add": {"component_groups": ["minecraft:cow_baby"]}}]}, "minecraft:entity_born": {"add": {"component_groups": ["minecraft:cow_baby"]}}, "minecraft:entity_transformed": {"remove": {}, "add": {"component_groups": ["minecraft:cow_adult"]}}, "minecraft:ageable_grow_up": {"remove": {"component_groups": ["minecraft:cow_baby"]}, "add": {"component_groups": ["minecraft:cow_adult"]}}, "on:death": {"run_command": {"command": []}}, "on:hurt": {"run_command": {"command": []}}, "on:hurt_by_player": {"run_command": {"command": []}}, "on:ignite": {"run_command": {"command": []}}, "on:target_acquired": {"run_command": {"command": []}}, "on:target_escape": {"run_command": {"command": []}}, "on:wake_with_owner": {"run_command": {"command": []}}}}}