{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.eagle", "texture_width": 64, "texture_height": 64, "visible_bounds_width": 5, "visible_bounds_height": 4, "visible_bounds_offset": [0, 1, 0]}, "bones": [{"name": "into_morph", "pivot": [0, 0, 0]}, {"name": "eagle", "parent": "into_morph", "pivot": [0, 0, 0]}, {"name": "body", "parent": "eagle", "pivot": [0, 9, 1], "rotation": [55, 0, 0], "cubes": [{"origin": [-2.5, 3, -1], "size": [5, 10, 5], "inflate": 0.15, "uv": [0, 26]}]}, {"name": "head", "parent": "body", "pivot": [0, 11.1, 0.8], "cubes": [{"origin": [-1.5, 11.95, -3.45], "size": [3, 4, 5], "pivot": [0, 7.1, 0.3], "rotation": [-25, 0, 0], "uv": [0, 41]}]}, {"name": "cabeza", "parent": "head", "pivot": [0, 14.9, 3.3], "rotation": [-55, 0, 0], "cubes": [{"origin": [-1, 14, 0.8], "size": [2, 3, 3], "uv": [0, 0]}]}, {"name": "cabeza0", "parent": "cabeza", "pivot": [0, 16.9, 1.3], "cubes": [{"origin": [-1.5, 17, -0.2], "size": [3, 3, 4], "uv": [40, 38]}]}, {"name": "pico", "parent": "cabeza0", "pivot": [0, 19, 1], "rotation": [-11, 0, 0], "cubes": [{"origin": [-1, 18, -2], "size": [2, 1, 2], "inflate": 0.025, "pivot": [0, 19, 1], "rotation": [25, 0, 0], "uv": [22, 14]}, {"origin": [-1.5, 17.1, -0.1], "size": [3, 1, 1], "inflate": 0.05, "pivot": [0, 18.4, 0.9], "rotation": [10, 0, 0], "uv": [22, 17]}, {"origin": [-1, 16.75, -3.7], "size": [2, 1, 2], "pivot": [0, 17.725, -1.725], "rotation": [33.5, 0, 0], "uv": [22, 8]}, {"origin": [-1, 15.95, -3.7], "size": [2, 1, 1], "inflate": -0.025, "pivot": [0, 17.725, -1.725], "rotation": [33.5, 0, 0], "uv": [0, 23]}, {"origin": [-1, 17, -3.1], "size": [2, 1, 3], "inflate": -0.25, "pivot": [0, 19, 1], "rotation": [17.5, 0, 0], "uv": [0, 19]}]}, {"name": "tail", "parent": "body", "pivot": [0, 2.9, 3.7], "rotation": [-90, 0, 0], "cubes": [{"origin": [-2.5, 2.33288, 3.74741], "size": [5, 1, 12], "uv": [22, 1]}, {"origin": [-5.475, 2.33288, 3.74741], "size": [5, 1, 12], "inflate": -0.025, "pivot": [-1.5, 3.33288, 10.74741], "rotation": [0, -22.5, 0], "uv": [0, 13]}, {"origin": [0.45, 2.33288, 3.74741], "size": [5, 1, 12], "inflate": -0.025, "pivot": [1.5, 3.33288, 10.74741], "rotation": [0, 22.5, 0], "uv": [0, 0]}]}, {"name": "ala0", "parent": "body", "pivot": [2.5, 13, 1.5], "cubes": [{"origin": [2, 7, -3], "size": [1, 6, 8], "uv": [30, 38]}]}, {"name": "ala0_1", "parent": "ala0", "pivot": [3, 7, 2.5], "cubes": [{"origin": [2, 1, -3], "size": [1, 6, 8], "uv": [36, 24]}]}, {"name": "ala0_2", "parent": "ala0_1", "pivot": [3, 1, 0.5], "cubes": [{"origin": [2, -4, -3], "size": [1, 5, 6], "uv": [10, 46]}]}, {"name": "ala1", "parent": "body", "pivot": [-2.5, 13, 1.5], "cubes": [{"origin": [-3, 7, -3], "size": [1, 6, 8], "uv": [20, 32]}]}, {"name": "ala0_3", "parent": "ala1", "pivot": [-3, 7, 2.5], "cubes": [{"origin": [-3, 1, -3], "size": [1, 6, 8], "uv": [26, 18]}]}, {"name": "ala0_4", "parent": "ala0_3", "pivot": [-3, 1, 0.5], "cubes": [{"origin": [-3, -4, -3], "size": [1, 5, 6], "uv": [44, 0]}]}, {"name": "leg1", "parent": "eagle", "pivot": [-2, 7.1, 0], "cubes": [{"origin": [-3.5, 4, -0.5], "size": [3, 5, 4], "uv": [36, 14]}]}, {"name": "leg0_1", "parent": "leg1", "pivot": [-2, 4, 1], "cubes": [{"origin": [-3, 1.4, -0.75], "size": [2, 3, 3], "pivot": [0, 2.5, 1], "rotation": [-21.5, 0, 0], "uv": [0, 6]}, {"origin": [-3, 0.6, -0.75], "size": [2, 1, 3], "inflate": -0.15, "pivot": [0, 2.5, 1], "rotation": [-21.5, 0, 0], "uv": [54, 0]}, {"origin": [-3, 0, 0], "size": [2, 2, 2], "inflate": -0.25, "uv": [22, 0]}]}, {"name": "dedo4", "parent": "leg0_1", "pivot": [-2, 0.6, 0], "cubes": [{"origin": [-2.5, 0.325, -4.7], "size": [1, 1, 2], "inflate": -0.2, "pivot": [-2, 0.6, -2.8], "rotation": [31, 0, 0], "uv": [20, 30]}, {"origin": [-2.5, 0.1, -1.5], "size": [1, 1, 2], "uv": [30, 14]}, {"origin": [-2.5, 0.1, -3.3], "size": [1, 1, 2], "inflate": -0.1, "uv": [28, 17]}]}, {"name": "dedo5", "parent": "leg0_1", "pivot": [-3, 0.6, 0], "rotation": [0, 15, 0], "cubes": [{"origin": [-3.5, 0.325, -4.7], "size": [1, 1, 2], "inflate": -0.2, "pivot": [-3, 0.6, -2.8], "rotation": [31, 0, 0], "uv": [28, 9]}, {"origin": [-3.5, 0.1, -1.5], "size": [1, 1, 2], "uv": [28, 6]}, {"origin": [-3.5, 0.1, -3.3], "size": [1, 1, 2], "inflate": -0.1, "uv": [28, 2]}]}, {"name": "dedo6", "parent": "leg0_1", "pivot": [-1, 0.6, 0], "rotation": [0, -15, 0], "cubes": [{"origin": [-1.5, 0.325, -4.7], "size": [1, 1, 2], "inflate": -0.2, "pivot": [-1, 0.6, -2.8], "rotation": [31, 0, 0], "uv": [19, 27]}, {"origin": [-1.5, 0.1, -1.5], "size": [1, 1, 2], "uv": [26, 20]}, {"origin": [-1.5, 0.1, -3.3], "size": [1, 1, 2], "inflate": -0.1, "uv": [15, 26]}]}, {"name": "dedo7", "parent": "leg0_1", "pivot": [-2, 0.6, 1], "rotation": [0, 180, 0], "cubes": [{"origin": [-2.5, 0.325, -2.7], "size": [1, 1, 2], "inflate": -0.2, "pivot": [-2, 0.6, -0.8], "rotation": [31, 0, 0], "uv": [22, 22]}, {"origin": [-2.5, 0.1, 0.5], "size": [1, 1, 1], "uv": [7, 0]}, {"origin": [-2.5, 0.1, -1.3], "size": [1, 1, 2], "inflate": -0.1, "uv": [22, 19]}]}, {"name": "leg0", "parent": "eagle", "pivot": [2, 7.1, 0], "cubes": [{"origin": [0.5, 4, -0.5], "size": [3, 5, 4], "uv": [46, 19]}]}, {"name": "leg0_0", "parent": "leg0", "pivot": [2, 4, 1], "cubes": [{"origin": [1, 1.4, -0.75], "size": [2, 3, 3], "pivot": [0, 2.5, 1], "rotation": [-21.5, 0, 0], "uv": [0, 13]}, {"origin": [1, 0.6, -0.75], "size": [2, 1, 3], "inflate": -0.15, "pivot": [0, 2.5, 1], "rotation": [-21.5, 0, 0], "uv": [54, 28]}, {"origin": [1, 0, 0], "size": [2, 2, 2], "inflate": -0.25, "uv": [22, 4]}]}, {"name": "dedo0", "parent": "leg0_0", "pivot": [2, 0.6, 0], "cubes": [{"origin": [1.5, 0.325, -4.7], "size": [1, 1, 2], "inflate": -0.2, "pivot": [2, 0.6, -2.8], "rotation": [31, 0, 0], "uv": [44, 3]}, {"origin": [1.5, 0.1, -1.5], "size": [1, 1, 2], "uv": [18, 46]}, {"origin": [1.5, 0.1, -3.3], "size": [1, 1, 2], "inflate": -0.1, "uv": [46, 14]}]}, {"name": "dedo1", "parent": "leg0_0", "pivot": [1, 0.6, 0], "rotation": [0, 15, 0], "cubes": [{"origin": [0.5, 0.325, -4.7], "size": [1, 1, 2], "inflate": -0.2, "pivot": [1, 0.6, -2.8], "rotation": [31, 0, 0], "uv": [44, 0]}, {"origin": [0.5, 0.1, -1.5], "size": [1, 1, 2], "uv": [11, 41]}, {"origin": [0.5, 0.1, -3.3], "size": [1, 1, 2], "inflate": -0.1, "uv": [36, 23]}]}, {"name": "dedo2", "parent": "leg0_0", "pivot": [3, 0.6, 0], "rotation": [0, -15, 0], "cubes": [{"origin": [2.5, 0.325, -4.7], "size": [1, 1, 2], "inflate": -0.2, "pivot": [3, 0.6, -2.8], "rotation": [31, 0, 0], "uv": [20, 36]}, {"origin": [2.5, 0.1, -1.5], "size": [1, 1, 2], "uv": [30, 35]}, {"origin": [2.5, 0.1, -3.3], "size": [1, 1, 2], "inflate": -0.1, "uv": [34, 15]}]}, {"name": "dedo3", "parent": "leg0_0", "pivot": [2, 0.6, 1], "rotation": [0, 180, 0], "cubes": [{"origin": [1.5, 0.325, -2.7], "size": [1, 1, 2], "inflate": -0.2, "pivot": [2, 0.6, -0.8], "rotation": [31, 0, 0], "uv": [20, 33]}, {"origin": [1.5, 0.1, 0.5], "size": [1, 1, 1], "uv": [7, 6]}, {"origin": [1.5, 0.1, -1.3], "size": [1, 1, 2], "inflate": -0.1, "uv": [30, 32]}]}, {"name": "root", "pivot": [0, 0, 0]}, {"name": "rotation", "parent": "root", "pivot": [0, 0, 0]}, {"name": "waist2", "parent": "rotation", "pivot": [0, 12, 0]}, {"name": "body_human", "parent": "waist2", "pivot": [0, 24, 0]}, {"name": "leftArm", "parent": "body_human", "pivot": [5, 22, 0]}, {"name": "la", "parent": "leftArm", "pivot": [6, 18, 0]}, {"name": "leftItem", "parent": "la", "pivot": [6, 15, 1]}, {"name": "rightArm", "parent": "body_human", "pivot": [-5, 22, 0]}, {"name": "ra", "parent": "rightArm", "pivot": [-6, 18, 0]}, {"name": "rightItem", "parent": "ra", "pivot": [-6, 17, 2]}]}]}