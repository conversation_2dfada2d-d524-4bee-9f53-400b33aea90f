{"tiers": [{"groups": [{"num_to_select": 5, "trades": [{"max_uses": 16, "wants": [{"item": "minecraft:emerald", "quantity": 2}], "gives": [{"item": "worldanimals:elephant_saddle"}]}, {"max_uses": 16, "wants": [{"item": "minecraft:emerald", "quantity": 5}], "gives": [{"item": "worldanimals:mammoth_dna"}]}, {"max_uses": 16, "wants": [{"item": "minecraft:emerald", "quantity": 2}], "gives": [{"item": "worldanimals:swordfish_death"}]}, {"max_uses": 16, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "worldanimals:syringe"}]}, {"max_uses": 16, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "worldanimals:hat"}]}, {"max_uses": 8, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "worldanimals:cooked_ostrich_leg", "quantity": 7}]}, {"max_uses": 8, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "worldanimals:reptil_skin"}]}, {"max_uses": 16, "wants": [{"item": "minecraft:emerald", "quantity": 7}], "gives": [{"item": "worldanimals:diamond_rhinoceros_saddle"}]}, {"max_uses": 16, "wants": [{"item": "minecraft:emerald", "quantity": 3}], "gives": [{"item": "worldanimals:ostrich_saddle"}]}, {"max_uses": 8, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "worldanimals:collar"}]}, {"max_uses": 16, "wants": [{"item": "minecraft:emerald", "quantity": 3}], "gives": [{"item": "worldanimals:big_cat_saddle"}]}, {"max_uses": 8, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "fortniteaddon:banana"}]}, {"max_uses": 16, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"choice": [{"item": "minecraft:yellow_flower"}, {"item": "minecraft:red_flower", "functions": [{"function": "random_block_state", "block_state": "flower_type", "values": {"min": 0, "max": 10}}]}, {"item": "minecraft:double_plant:0"}, {"item": "minecraft:double_plant:1"}, {"item": "minecraft:double_plant:4"}, {"item": "minecraft:double_plant:5"}]}]}, {"max_uses": 16, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"choice": [{"item": "minecraft:wheat_seeds"}, {"item": "minecraft:pumpkin_seeds"}, {"item": "minecraft:melon_seeds"}, {"item": "minecraft:beetroot_seeds"}]}]}, {"max_uses": 16, "wants": [{"item": "minecraft:emerald", "quantity": 5}], "gives": [{"item": "minecraft:sapling", "functions": [{"function": "random_block_state", "block_state": "sapling_type", "values": {"min": 0, "max": 5}}]}]}, {"max_uses": 16, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:dye", "quantity": 3, "functions": [{"function": "random_aux_value", "values": {"min": 0, "max": 15}}]}]}, {"max_uses": 8, "wants": [{"item": "minecraft:emerald"}], "gives": [{"item": "minecraft:coral_block", "quantity": 3, "functions": [{"function": "random_block_state", "block_state": "coral_color", "values": {"min": 0, "max": 4}}]}]}, {"max_uses": 8, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:vine"}]}, {"max_uses": 16, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"choice": [{"item": "worldanimals:zebra_skin"}, {"item": "worldanimals:zebra_skin"}]}]}, {"max_uses": 16, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:waterlily", "quantity": 2}]}, {"max_uses": 16, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:sand:1", "quantity": 4}]}, {"max_uses": 16, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:red_sandstone", "quantity": 4}]}]}, {"num_to_select": 1, "trades": [{"max_uses": 4, "wants": [{"item": "minecraft:emerald", "quantity": 5}], "gives": [{"item": "minecraft:bucket:4"}]}, {"max_uses": 4, "wants": [{"item": "minecraft:emerald", "quantity": 5}], "gives": [{"item": "minecraft:bucket:5"}]}, {"max_uses": 8, "wants": [{"item": "minecraft:emerald", "quantity": 3}], "gives": [{"item": "minecraft:packed_ice"}]}, {"max_uses": 8, "wants": [{"item": "minecraft:emerald", "quantity": 6}], "gives": [{"item": "minecraft:blue_ice"}]}, {"max_uses": 16, "wants": [{"item": "minecraft:emerald", "quantity": 1}], "gives": [{"item": "minecraft:gunpowder"}]}, {"max_uses": 8, "wants": [{"item": "minecraft:emerald", "quantity": 3}], "gives": [{"item": "minecraft:podzol", "quantity": 3}]}]}]}]}