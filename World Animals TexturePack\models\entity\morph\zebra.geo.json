{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.zebra", "texture_width": 128, "texture_height": 128, "visible_bounds_width": 5, "visible_bounds_height": 3.5, "visible_bounds_offset": [0, 1.25, 0]}, "bones": [{"name": "into_morph", "pivot": [0, 0, 0]}, {"name": "zebra", "parent": "into_morph", "pivot": [0, 0, -1]}, {"name": "body_cebra", "parent": "zebra", "pivot": [0, 12, -1], "cubes": [{"origin": [-4.5, 11.75, -8], "size": [9, 9, 12], "uv": [0, 0]}, {"origin": [-5, 12, 3.975], "size": [10, 10, 10], "uv": [0, 21]}, {"origin": [-4, 20.7, -6], "size": [8, 1, 10], "uv": [30, 0]}]}, {"name": "head", "parent": "body_cebra", "pivot": [0, 19, -8]}, {"name": "cabeza", "parent": "head", "pivot": [0, 19, -8], "rotation": [22.5, 0, 0], "cubes": [{"origin": [-2, 15, -11], "size": [4, 10, 6], "uv": [36, 15]}, {"origin": [-2.5, 25, -13], "size": [5, 6, 8], "uv": [32, 33]}, {"origin": [-1, 17.025, -9], "size": [2, 8, 6], "inflate": -0.025, "uv": [0, 41]}, {"origin": [-1, 25, -5], "size": [2, 8, 2], "uv": [0, 21]}, {"origin": [-1, 30, -7], "size": [2, 3, 2], "uv": [30, 5]}, {"origin": [-2, 26, -16], "size": [4, 4, 3], "uv": [58, 44]}, {"origin": [-2, 27, -19], "size": [4, 3, 3], "inflate": 0.125, "uv": [59, 55]}]}, {"name": "boca0", "parent": "cabeza", "pivot": [0, 28, -16], "cubes": [{"origin": [-2, 26, -19], "size": [4, 1, 3], "inflate": 0.075, "uv": [42, 11]}]}, {"name": "oreja1", "parent": "cabeza", "pivot": [-2, 30.27476, -7.13236], "rotation": [0, 0, -27.5], "cubes": [{"origin": [-3, 29.25, -7], "size": [2, 4, 1], "uv": [30, 21]}]}, {"name": "oreja0", "parent": "cabeza", "pivot": [2, 30.27476, -7.13236], "rotation": [0, 0, 27.5], "cubes": [{"origin": [1, 29.25, -7], "size": [2, 4, 1], "uv": [30, 26]}]}, {"name": "tail", "parent": "body_cebra", "pivot": [0, 20.8, 13.4], "rotation": [-52.5, 0, 0], "cubes": [{"origin": [-1, 19.84399, 13.40024], "size": [2, 2, 4], "uv": [56, 61]}]}, {"name": "tail2", "parent": "tail", "pivot": [0, 20.84399, 17.40024], "rotation": [-20, 0, 0], "cubes": [{"origin": [-1, 19.84399, 17.00024], "size": [2, 2, 6], "inflate": -0.1, "uv": [50, 11]}]}, {"name": "tail3", "parent": "tail2", "pivot": [0, 20.84399, 23.00024], "rotation": [-7.5, 0, 0], "cubes": [{"origin": [-1, 19.84399, 22.60024], "size": [2, 2, 3], "inflate": 0.15, "uv": [30, 0]}, {"origin": [-1, 19.84399, 25.77524], "size": [2, 2, 4], "inflate": 0.125, "uv": [60, 11]}]}, {"name": "leg3", "parent": "zebra", "pivot": [4, 12, 10.4], "cubes": [{"origin": [2.5, 8.5, 8.8], "size": [3, 8, 5], "inflate": 0.15, "pivot": [4, 9.5, 12.4], "rotation": [12.5, 0, 0], "uv": [16, 41]}]}, {"name": "leg3_0", "parent": "leg3", "pivot": [4, 8.5, 11.5], "cubes": [{"origin": [2.5, 1, 8.5], "size": [3, 9, 3], "pivot": [4, 4, 8.4], "rotation": [-20, 0, 0], "uv": [0, 0]}, {"origin": [2, 0, 6.65], "size": [4, 2, 4], "uv": [56, 0]}]}, {"name": "leg2", "parent": "zebra", "pivot": [-4, 12, 10.4], "cubes": [{"origin": [-5.5, 8.5, 8.8], "size": [3, 8, 5], "inflate": 0.15, "pivot": [-4, 9.5, 12.4], "rotation": [12.5, 0, 0], "uv": [32, 47]}]}, {"name": "leg2_0", "parent": "leg2", "pivot": [-4, 8.5, 11.5], "cubes": [{"origin": [-5.5, 1, 8.5], "size": [3, 9, 3], "pivot": [-4, 4, 8.4], "rotation": [-20, 0, 0], "uv": [13, 54]}, {"origin": [-6, 0, 6.65], "size": [4, 2, 4], "uv": [56, 19]}]}, {"name": "leg1", "parent": "zebra", "pivot": [-3.5, 12, -6], "cubes": [{"origin": [-5, 7.5, -8.25], "size": [3, 7, 4], "inflate": 0.25, "pivot": [-3.5, 9.5, -6.6], "rotation": [2.5, 0, 0], "uv": [48, 47]}]}, {"name": "leg1_0", "parent": "leg1", "pivot": [-3.5, 7.5, -6.5], "cubes": [{"origin": [-5, 1.5, -9], "size": [3, 7, 3], "pivot": [-3.5, 4, -8.6], "rotation": [-20, 0, 0], "uv": [0, 55]}, {"origin": [-5.5, 0, -10.85], "size": [4, 2, 4], "uv": [58, 38]}]}, {"name": "leg0", "parent": "zebra", "pivot": [3.5, 12, -6], "cubes": [{"origin": [2, 7.5, -8.25], "size": [3, 7, 4], "inflate": 0.25, "pivot": [3.5, 9.5, -6.6], "rotation": [2.5, 0, 0], "uv": [52, 27]}]}, {"name": "leg0_0", "parent": "leg0", "pivot": [3.5, 7.5, -6.5], "cubes": [{"origin": [2, 1.5, -9], "size": [3, 7, 3], "pivot": [3.5, 4, -8.6], "rotation": [-20, 0, 0], "uv": [25, 60]}, {"origin": [1.5, 0, -10.85], "size": [4, 2, 4], "uv": [44, 58]}]}, {"name": "root", "pivot": [0, 0, 0]}, {"name": "rotation", "parent": "root", "pivot": [0, 0, 0]}, {"name": "waist", "parent": "rotation", "pivot": [0, 12, 0]}, {"name": "body", "parent": "waist", "pivot": [0, 24, 0]}, {"name": "leftArm", "parent": "body", "pivot": [5, 22, 0]}, {"name": "la", "parent": "leftArm", "pivot": [6, 18, 0]}, {"name": "leftItem", "parent": "la", "pivot": [6, 15, 1]}, {"name": "rightArm", "parent": "body", "pivot": [-5, 22, 0]}, {"name": "ra", "parent": "rightArm", "pivot": [-6, 18, 0]}, {"name": "rightItem", "parent": "ra", "pivot": [-6, 17, 2]}]}]}