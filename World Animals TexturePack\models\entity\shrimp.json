{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.shrimp", "texture_width": 64, "texture_height": 64, "visible_bounds_width": 3, "visible_bounds_height": 3, "visible_bounds_offset": [0, 0.5, 0]}, "bones": [{"name": "shrimp", "pivot": [0, 6, 0], "rotation": [92.5, 0, 0]}, {"name": "head", "parent": "shrimp", "pivot": [0, 9, 0], "cubes": [{"origin": [-1, 9, -0.5], "size": [2, 4, 2], "pivot": [0, 9, -0.5], "rotation": [25, 0, 0], "uv": [0, 21]}, {"origin": [-1.75, 10, -0.5], "size": [2, 2, 2], "inflate": -0.5, "pivot": [-2, 9, -0.5], "rotation": [25, 0, 0], "uv": [0, 27]}, {"origin": [-0.25, 10, -0.5], "size": [2, 2, 2], "inflate": -0.5, "pivot": [0, 9, -0.5], "rotation": [25, 0, 0], "uv": [26, 22]}, {"origin": [0, 12, -0.5], "size": [1, 6, 1], "inflate": -0.45, "pivot": [0, 9, -0.5], "rotation": [25, 0, 0], "uv": [12, 28]}, {"origin": [-1, 12, -0.5], "size": [1, 6, 1], "inflate": -0.45, "pivot": [-1, 9, -0.5], "rotation": [25, 0, 0], "uv": [8, 27]}]}, {"name": "bone", "parent": "shrimp", "pivot": [0, 4, 0], "cubes": [{"origin": [-1.5, 4, -1.5], "size": [3, 6, 3], "uv": [0, 0]}]}, {"name": "tail", "parent": "bone", "pivot": [0, 4.8, 0.7], "rotation": [-45, 0, 0], "cubes": [{"origin": [-1.5, -1.8, -0.8], "size": [3, 4, 3], "inflate": -0.5, "uv": [12, 0]}, {"origin": [-2.5, -3.8, 0.2], "size": [5, 3, 1], "inflate": -0.25, "uv": [11, 8]}, {"origin": [-1.5, 1.2, -0.8], "size": [3, 4, 3], "inflate": -0.4, "uv": [0, 9]}]}, {"name": "feets", "parent": "bone", "pivot": [0, 0, 0]}, {"name": "feet0", "parent": "feets", "pivot": [1, 9, -1.5], "cubes": [{"origin": [0.5, 8.5, -4.5], "size": [1, 1, 4], "inflate": -0.25, "uv": [20, 3]}, {"origin": [0.5, 8.2, -6.5], "size": [1, 1, 3], "inflate": -0.35, "pivot": [-2, 8.5, -5], "rotation": [20, 0, 0], "uv": [26, 12]}]}, {"name": "feet1", "parent": "feets", "pivot": [1, 8, -1.5], "cubes": [{"origin": [0.5, 7.5, -4.5], "size": [1, 1, 4], "inflate": -0.25, "uv": [18, 19]}, {"origin": [0.5, 7.2, -6.5], "size": [1, 1, 3], "inflate": -0.35, "pivot": [-2, 7.5, -5], "rotation": [20, 0, 0], "uv": [26, 0]}]}, {"name": "feet2", "parent": "feets", "pivot": [1, 7, -1.5], "cubes": [{"origin": [0.5, 6.5, -4.5], "size": [1, 1, 4], "inflate": -0.25, "uv": [19, 8]}, {"origin": [0.5, 6.2, -6.5], "size": [1, 1, 3], "inflate": -0.35, "pivot": [-2, 6.5, -5], "rotation": [20, 0, 0], "uv": [25, 8]}]}, {"name": "feet3", "parent": "feets", "pivot": [1, 6, -1.5], "cubes": [{"origin": [0.5, 5.5, -4.5], "size": [1, 1, 4], "inflate": -0.25, "uv": [12, 18]}, {"origin": [0.5, 5.2, -6.5], "size": [1, 1, 3], "inflate": -0.35, "pivot": [-2, 5.5, -5], "rotation": [20, 0, 0], "uv": [21, 24]}]}, {"name": "feets2", "parent": "bone", "pivot": [-2, 0, 0]}, {"name": "feet4", "parent": "feets2", "pivot": [-1, 9, -1.5], "cubes": [{"origin": [-1.5, 8.5, -4.5], "size": [1, 1, 4], "inflate": -0.25, "uv": [6, 17]}, {"origin": [-1.5, 8.2, -6.5], "size": [1, 1, 3], "inflate": -0.35, "pivot": [-4, 8.5, -5], "rotation": [20, 0, 0], "uv": [24, 17]}]}, {"name": "feet5", "parent": "feets2", "pivot": [-1, 8, -1.5], "cubes": [{"origin": [-1.5, 7.5, -4.5], "size": [1, 1, 4], "inflate": -0.25, "uv": [0, 16]}, {"origin": [-1.5, 7.2, -6.5], "size": [1, 1, 3], "inflate": -0.35, "pivot": [-4, 7.5, -5], "rotation": [20, 0, 0], "uv": [13, 24]}]}, {"name": "feet6", "parent": "feets2", "pivot": [-1, 7, -1.5], "cubes": [{"origin": [-1.5, 6.5, -4.5], "size": [1, 1, 4], "inflate": -0.25, "uv": [14, 13]}, {"origin": [-1.5, 6.2, -6.5], "size": [1, 1, 3], "inflate": -0.35, "pivot": [-4, 6.5, -5], "rotation": [20, 0, 0], "uv": [8, 23]}]}, {"name": "feet7", "parent": "feets2", "pivot": [-1, 6, -1.5], "cubes": [{"origin": [-1.5, 5.5, -4.5], "size": [1, 1, 4], "inflate": -0.25, "uv": [8, 12]}, {"origin": [-1.5, 5.2, -6.5], "size": [1, 1, 3], "inflate": -0.35, "pivot": [-4, 5.5, -5], "rotation": [20, 0, 0], "uv": [20, 13]}]}]}]}