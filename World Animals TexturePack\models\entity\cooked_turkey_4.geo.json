{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.cooked_turkey_4", "texture_width": 128, "texture_height": 128, "visible_bounds_width": 3, "visible_bounds_height": 2.5, "visible_bounds_offset": [0, 0.75, 0]}, "bones": [{"name": "plato", "pivot": [0, 0, 1], "cubes": [{"origin": [-8, 0, -10], "size": [16, 1, 20], "uv": [0, 0]}]}, {"name": "pavo", "parent": "plato", "pivot": [0, 1, -1], "cubes": [{"origin": [-5, 1, 3], "size": [10, 8, 3], "uv": [9, 30]}, {"origin": [0.5, 8.5, 3.3], "size": [4, 1, 2], "inflate": 0.25, "uv": [42, 40]}, {"origin": [-4.5, 8.5, 3.3], "size": [4, 1, 2], "inflate": 0.25, "uv": [40, 29]}]}]}]}