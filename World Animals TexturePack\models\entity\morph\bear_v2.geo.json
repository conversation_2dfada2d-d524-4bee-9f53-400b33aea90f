{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.bear_v2", "texture_width": 128, "texture_height": 128, "visible_bounds_width": 4, "visible_bounds_height": 4.5, "visible_bounds_offset": [0, 1.75, 0]}, "bones": [{"name": "into_morph", "pivot": [0, 0, 0]}, {"name": "bear", "parent": "into_morph", "pivot": [0, 0, 0]}, {"name": "leg1", "parent": "bear", "pivot": [-4.5, 14, 6.5], "cubes": [{"origin": [-6, 6.6, 4.5], "size": [5, 8, 5], "pivot": [-3.5, 11, 8], "rotation": [15, 0, 0], "uv": [48, 49]}]}, {"name": "leg0_1", "parent": "leg1", "pivot": [-3.5, 7, 8], "cubes": [{"origin": [-5.5, 2, 6], "size": [4, 5, 4], "uv": [64, 9]}]}, {"name": "pat1", "parent": "leg0_1", "pivot": [-3.5, 2, 8.6], "cubes": [{"origin": [-6, 0, 5.1], "size": [5, 2, 5], "uv": [61, 34]}]}, {"name": "leg0", "parent": "bear", "pivot": [4.5, 14, 6.5], "cubes": [{"origin": [1, 6.6, 4.5], "size": [5, 8, 5], "pivot": [3.5, 11, 8], "rotation": [15, 0, 0], "uv": [0, 55]}]}, {"name": "leg0_0", "parent": "leg0", "pivot": [3.5, 7, 8], "cubes": [{"origin": [1.5, 2, 6], "size": [4, 5, 4], "uv": [66, 67]}]}, {"name": "pat0", "parent": "leg0_0", "pivot": [3.5, 2, 8.6], "cubes": [{"origin": [1, 0, 5.1], "size": [5, 2, 5], "uv": [15, 63]}]}, {"name": "waist", "parent": "bear", "pivot": [0, 13, 8], "cubes": [{"origin": [-6, 8.6, -9.1], "size": [12, 10, 12], "uv": [0, 0]}, {"origin": [-5, 17.3, -8.9], "size": [10, 3, 6], "inflate": 0.4, "pivot": [0, 18.6, 2.9], "rotation": [8, 0, 0], "uv": [32, 22]}, {"origin": [-5.5, 8.5, 2], "size": [11, 9, 8], "pivot": [0, 13, 6.5], "rotation": [-10, 0, 0], "uv": [2, 24]}, {"origin": [-1, 15.5, 11.5], "size": [2, 2, 2], "inflate": 0.1, "pivot": [0, 12.46788, 11.39719], "rotation": [15, 0, 0], "uv": [58, 21]}]}, {"name": "head", "parent": "waist", "pivot": [0, 14.2, -8], "cubes": [{"origin": [-4.5, 12.2, -15], "size": [9, 8, 6], "pivot": [0, 12.2, -13], "rotation": [-22, 0, 0], "uv": [36, 35]}]}, {"name": "cabeza", "parent": "head", "pivot": [0, 16.2, -12], "cubes": [{"origin": [-2, 13.8, -22], "size": [4, 3, 4], "uv": [36, 62]}, {"origin": [-4, 12.2, -18], "size": [8, 8, 6], "uv": [0, 41]}, {"origin": [3, 20, -14], "size": [3, 3, 1], "pivot": [2.5, 21.7, -13.5], "rotation": [0, -17.5, 37.5], "uv": [50, 31]}, {"origin": [-6, 20, -14], "size": [3, 3, 1], "pivot": [-2.5, 21.7, -13.5], "rotation": [0, 17.5, -37.5], "uv": [42, 31]}]}, {"name": "boca", "parent": "cabeza", "pivot": [0, 13.9, -18], "cubes": [{"origin": [-2, 12.2, -21.8], "size": [4, 2, 4], "inflate": -0.2, "uv": [66, 18]}]}, {"name": "leg2", "parent": "waist", "pivot": [2.7, 14, -6.7], "cubes": [{"origin": [1.2, 7, -9.2], "size": [5, 8, 5], "uv": [28, 49]}]}, {"name": "leg0_2", "parent": "leg2", "pivot": [3.7, 7, -6.7], "cubes": [{"origin": [1.7, 2, -8.7], "size": [4, 5, 4], "uv": [63, 45]}]}, {"name": "pat2", "parent": "leg0_2", "pivot": [3.7, 2, -6.1], "cubes": [{"origin": [1.2, 0, -9.6], "size": [5, 2, 5], "uv": [59, 26]}]}, {"name": "leg3", "parent": "waist", "pivot": [-4.7, 14, -6.7], "cubes": [{"origin": [-6.2, 7, -9.2], "size": [5, 8, 5], "uv": [48, 0]}]}, {"name": "leg0_3", "parent": "leg3", "pivot": [-3.7, 7, -6.7], "cubes": [{"origin": [-5.7, 2, -8.7], "size": [4, 5, 4], "uv": [54, 62]}]}, {"name": "pat3", "parent": "leg0_3", "pivot": [-3.7, 2, -6.1], "cubes": [{"origin": [-6.2, 0, -9.6], "size": [5, 2, 5], "uv": [49, 14]}]}, {"name": "root", "pivot": [0, 0, 0]}, {"name": "rotation", "parent": "root", "pivot": [0, 0, 0]}, {"name": "waist2", "parent": "rotation", "pivot": [0, 12, 0]}, {"name": "body_human", "parent": "waist2", "pivot": [0, 24, 0]}, {"name": "leftArm", "parent": "body_human", "pivot": [5, 22, 0]}, {"name": "la", "parent": "leftArm", "pivot": [6, 18, 0]}, {"name": "leftItem", "parent": "la", "pivot": [6, 15, 1]}, {"name": "rightArm", "parent": "body_human", "pivot": [-5, 22, 0]}, {"name": "ra", "parent": "rightArm", "pivot": [-6, 18, 0]}, {"name": "rightItem", "parent": "ra", "pivot": [-6, 17, 2]}]}]}