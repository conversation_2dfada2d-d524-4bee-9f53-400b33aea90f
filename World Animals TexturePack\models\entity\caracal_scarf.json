{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.caracal_scarf", "texture_width": 128, "texture_height": 128, "visible_bounds_width": 4, "visible_bounds_height": 3.5, "visible_bounds_offset": [0, 1.25, 0]}, "bones": [{"name": "caracal", "pivot": [-0.5, 0, 2]}, {"name": "body", "parent": "caracal", "pivot": [-0.5, 10, 1]}, {"name": "tail", "parent": "body", "pivot": [0, 10, 6], "rotation": [-60, 0, 0]}, {"name": "tail0", "parent": "tail", "pivot": [0, 10, 11], "rotation": [22.5, 0, 0]}, {"name": "head", "parent": "body", "pivot": [-0.5, 13, -6]}, {"name": "boca", "parent": "head", "pivot": [0, 13.1, -9.75]}, {"name": "cabeza", "parent": "head", "pivot": [-0.5, 17, -5]}, {"name": "panuelo", "parent": "head", "pivot": [0, 10, -4.5], "cubes": [{"origin": [-3.5, 12.53223, -9.53223], "size": [7, 2, 7], "pivot": [-0.5, 13.03223, -5.03223], "rotation": [36, 0, 0], "uv": [0, 84]}, {"origin": [-3.5, 11.53223, -9.53223], "size": [7, 2, 7], "inflate": -0.1, "pivot": [-0.5, 13.03223, -5.03223], "rotation": [36, 0, 0], "uv": [0, 103]}, {"origin": [-0.5, 9.93223, -9.23223], "size": [2, 2, 1], "pivot": [-0.5, 13.03223, -5.03223], "rotation": [36, 0, 0], "uv": [0, 94]}, {"origin": [-0.5, 6.93223, -7.23223], "size": [2, 2, 1], "uv": [0, 94]}, {"origin": [-0.5, 4.93223, -7.23223], "size": [2, 2, 1], "uv": [7, 94]}]}, {"name": "leg3", "parent": "caracal", "pivot": [1.5, 7, 5]}, {"name": "leg2", "parent": "caracal", "pivot": [-2.5, 7, 5]}, {"name": "leg1", "parent": "caracal", "pivot": [1.5, 7, -4.5]}, {"name": "leg0", "parent": "caracal", "pivot": [-2.5, 7, -4.5]}]}]}