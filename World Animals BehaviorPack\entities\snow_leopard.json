{"format_version": "1.16.0", "minecraft:entity": {"description": {"identifier": "worldanimals:snow_leopard", "is_spawnable": true, "is_summonable": true, "is_experimental": false, "animations": {"big_cat": "animation.big_cat"}, "scripts": {"animate": ["big_cat"]}}, "component_groups": {"minecraft:snow_leopard_baby": {"minecraft:is_baby": {}, "minecraft:scale": {"value": 0.5}, "minecraft:ageable": {"duration": 1200, "feed_items": ["chicken", "cooked_chicken", "beef", "cooked_beef", "muttonRaw", "muttonCooked", "porkchop", "cooked_porkchop", "rabbit", "cooked_rabbit", "rotten_flesh"], "grow_up": {"event": "minecraft:ageable_grow_up", "target": "self"}}}, "minecraft:snow_leopard_adult": {"minecraft:experience_reward": {"on_bred": "<PERSON><PERSON>(1,7)", "on_death": "query.last_hit_by_player ? Math.Random(1,3) : 0"}, "minecraft:scale": {"value": 1.0}, "minecraft:loot": {"table": "loot_tables/entities/snow_leopard.json"}, "minecraft:behavior.breed": {"priority": 3, "speed_multiplier": 1.0}, "minecraft:breedable": {"require_tame": true, "require_full_health": true, "breeds_with": {"mate_type": "worldanimals:snow_leopard", "baby_type": "worldanimals:snow_leopard", "breed_event": {"event": "minecraft:entity_born", "target": "baby"}}, "breed_items": ["chicken", "cooked_chicken", "beef", "cooked_beef", "muttonRaw", "muttonCooked", "porkchop", "cooked_porkchop", "rabbit", "cooked_rabbit", "rotten_flesh"]}}, "minecraft:snow_leopard_unchested": {"minecraft:interact": {"interactions": [{"play_sounds": "armor.equip_generic", "on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_equipment", "domain": "hand", "subject": "other", "value": "worldanimals:big_cat_saddle"}]}, "event": "minecraft:on_chest", "target": "self"}, "use_item": true, "interact_text": "action.interact.attachchest"}]}}, "minecraft:snow_leopard_chested": {"minecraft:inventory": {"inventory_size": 27, "container_type": "container", "private": false}, "minecraft:horse.jump_strength": {"value": {"range_min": 0.5, "range_max": 0.78}}, "minecraft:rideable": {"seat_count": 1, "interact_text": "action.interact.mount", "family_types": ["player"], "seats": {"position": [0.0, 1.15, -0.2]}}, "minecraft:input_ground_controlled": {}, "minecraft:can_power_jump": {}, "minecraft:is_saddled": {}, "minecraft:behavior.player_ride_tamed": {}, "minecraft:loot": {"table": "loot_tables/entities/cat_saddle.json"}}, "minecraft:snow_leopard_wild": {"minecraft:behavior.avoid_mob_type": {"priority": 3, "entity_types": [{"filters": {"test": "is_family", "subject": "other", "value": "llama"}, "max_dist": 24, "walk_speed_multiplier": 1.5, "sprint_speed_multiplier": 1.5}], "probability_per_strength": 0.14}, "minecraft:tameable": {"probability": 1.0, "tame_items": "worldanimals:collar", "tame_event": {"event": "minecraft:on_tame", "target": "self"}}, "minecraft:behavior.nearest_attackable_target": {"priority": 4, "reselect_targets": true, "must_see": true, "entity_types": [{"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "zebra"}, {"test": "is_family", "subject": "other", "value": "sheep"}, {"test": "is_family", "subject": "other", "value": "rabbit"}]}, "max_dist": 16}]}}, "minecraft:snow_leopard_tame": {"minecraft:is_tamed": {}, "minecraft:health": {"value": 70, "max": 70}, "minecraft:behavior.follow_owner": {"priority": 6, "speed_multiplier": 1.0, "start_distance": 10, "stop_distance": 2}, "minecraft:attack": {"damage": 12}, "minecraft:behavior.breed": {"priority": 2, "speed_multiplier": 1.0}, "minecraft:behavior.owner_hurt_by_target": {"priority": 1}, "minecraft:behavior.owner_hurt_target": {"priority": 2}, "minecraft:behavior.nearest_attackable_target": {"priority": 5, "must_see": true, "entity_types": [{"filters": {"test": "is_family", "subject": "other", "value": "skeleton"}, "max_dist": 16}]}, "minecraft:sittable": {}, "minecraft:is_dyeable": {"interact_text": "action.interact.dye"}, "minecraft:leashable": {"soft_distance": 4.0, "hard_distance": 6.0, "max_distance": 10.0, "on_leash": {"event": "minecraft:on_leash", "target": "self"}, "on_unleash": {"event": "minecraft:on_unleash", "target": "self"}}}}, "components": {"minecraft:nameable": {}, "minecraft:variant": {"value": 0}, "minecraft:type_family": {"family": ["snow_leopard", "mob"]}, "minecraft:breathable": {"total_supply": 15, "suffocate_time": 0}, "minecraft:collision_box": {"width": 0.6, "height": 0.8}, "minecraft:health": {"value": 35, "max": 35}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:movement": {"value": 0.4}, "minecraft:navigation.walk": {"can_path_over_water": true, "avoid_damage_blocks": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:attack": {"damage": 8}, "minecraft:healable": {"items": [{"item": "porkchop", "heal_amount": 3}, {"item": "cooked_porkchop", "heal_amount": 8}, {"item": "fish", "heal_amount": 2}, {"item": "salmon", "heal_amount": 2}, {"item": "clownfish", "heal_amount": 1}, {"item": "pufferfish", "heal_amount": 1}, {"item": "cooked_fish", "heal_amount": 5}, {"item": "cooked_salmon", "heal_amount": 6}, {"item": "beef", "heal_amount": 3}, {"item": "cooked_beef", "heal_amount": 8}, {"item": "chicken", "heal_amount": 2}, {"item": "cooked_chicken", "heal_amount": 6}, {"item": "muttonRaw", "heal_amount": 2}, {"item": "muttonCooked", "heal_amount": 6}, {"item": "rotten_flesh", "heal_amount": 4}, {"item": "rabbit", "heal_amount": 3}, {"item": "cooked_rabbit", "heal_amount": 5}, {"item": "rabbit_stew", "heal_amount": 10}]}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.mount_pathing": {"priority": 1, "speed_multiplier": 1.25, "target_dist": 0, "track_target": true}, "minecraft:behavior.stay_while_sitting": {"priority": 3}, "minecraft:behavior.leap_at_target": {"priority": 4, "target_dist": 0.4}, "minecraft:behavior.melee_attack": {"priority": 5}, "minecraft:behavior.random_stroll": {"priority": 8, "speed_multiplier": 1.0}, "minecraft:behavior.look_at_player": {"priority": 6, "target_distance": 6.0, "probability": 0.02}, "minecraft:behavior.beg": {"priority": 9, "look_distance": 8, "look_time": [2, 4], "items": ["bone", "porkchop", "cooked_porkchop", "chicken", "cooked_chicken", "beef", "cooked_beef", "rotten_flesh", "muttonraw", "muttoncooked", "rabbit", "cooked_rabbit"]}, "minecraft:behavior.hurt_by_target": {"priority": 3}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}}, "events": {"minecraft:entity_spawned": {"randomize": [{"weight": 4, "remove": {}, "add": {"component_groups": ["minecraft:snow_leopard_adult", "minecraft:snow_leopard_wild"]}}, {"weight": 4, "remove": {}, "add": {"component_groups": ["minecraft:snow_leopard_adult", "minecraft:snow_leopard_wild"]}}, {"weight": 2, "remove": {}, "add": {"component_groups": ["minecraft:snow_leopard_baby", "minecraft:snow_leopard_wild"]}}]}, "minecraft:entity_born": {"remove": {}, "add": {"component_groups": ["minecraft:snow_leopard_baby", "minecraft:snow_leopard_tame"]}}, "minecraft:ageable_grow_up": {"remove": {"component_groups": ["minecraft:snow_leopard_baby"]}, "add": {"component_groups": ["minecraft:snow_leopard_adult"]}}, "minecraft:ageable_set_baby": {"remove": {"component_groups": ["minecraft:snow_leopard_adult"]}, "add": {"component_groups": ["minecraft:snow_leopard_baby"]}}, "minecraft:on_tame": {"remove": {"component_groups": ["minecraft:snow_leopard_wild"]}, "add": {"component_groups": ["minecraft:snow_leopard_tame", "minecraft:snow_leopard_unchested"]}}, "minecraft:on_chest": {"remove": {"component_groups": ["minecraft:snow_leopard_unchested"]}, "add": {"component_groups": ["minecraft:snow_leopard_chested"]}}}}}