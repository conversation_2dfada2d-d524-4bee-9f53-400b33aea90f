{"format_version": "1.10.0", "minecraft:spawn_rules": {"description": {"identifier": "pa:humpwhale", "population_control": "animal"}, "conditions": [{"minecraft:spawns_underground": {}, "minecraft:spawns_on_surface": {}, "minecraft:brightness_filter": {"min": 0.0, "max": 15.0, "adjust_for_weather": false}, "minecraft:weight": {"default": 2}, "minecraft:herd": {"min_size": 2, "max_size": 4}, "minecraft:density_limit": {"surface": 8, "underground": 8}, "minecraft:biome_filter": [{"any_of": [{"test": "has_biome_tag", "operator": "==", "value": "deep_ocean"}]}]}, {"minecraft:spawns_underground": {}, "minecraft:brightness_filter": {"min": 1, "max": 15, "adjust_for_weather": false}, "minecraft:weight": {"default": 2}, "minecraft:herd": {"min_size": 2, "max_size": 4}, "minecraft:density_limit": {"surface": 8, "underground": 8}, "minecraft:biome_filter": [{"any_of": [{"test": "has_biome_tag", "operator": "==", "value": "deep_ocean"}]}]}]}}