{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.capuchin_monkey", "texture_width": 32, "texture_height": 32, "visible_bounds_width": 4, "visible_bounds_height": 2.5, "visible_bounds_offset": [0, 0.75, 0]}, "bones": [{"name": "into_morph", "pivot": [0, 0, 0]}, {"name": "capuchin_monkey", "parent": "into_morph", "pivot": [0, 0, 0]}, {"name": "cuerpo", "parent": "capuchin_monkey", "pivot": [-1, 8, 0], "cubes": [{"origin": [-2, 5, -4], "size": [4, 4, 8], "uv": [0, 0]}]}, {"name": "head", "parent": "cuerpo", "pivot": [0, 8.5, -4], "cubes": [{"origin": [-1.5, 7, -7], "size": [3, 3, 3], "uv": [16, 0]}, {"origin": [0.775, 8, -6], "size": [1, 2, 2], "inflate": -0.25, "pivot": [1.5, 9, -5.75], "rotation": [0, 15, 0], "uv": [8, 12]}, {"origin": [-1.775, 8, -6], "size": [1, 2, 2], "inflate": -0.25, "pivot": [-1.5, 9, -5.75], "rotation": [0, -15, 0], "uv": [0, 12]}, {"origin": [-1, 6.775, -8], "size": [2, 2, 2], "inflate": -0.25, "uv": [0, 4]}]}, {"name": "tail", "parent": "cuerpo", "pivot": [0, 9, 4], "rotation": [-32.5, 0, 0], "cubes": [{"origin": [-0.5, 8, 4], "size": [1, 1, 6], "uv": [8, 13]}]}, {"name": "tail2", "parent": "tail", "pivot": [0, 8.775, 10], "rotation": [-27.5, 0, 0], "cubes": [{"origin": [-0.5, 7.975, 9.8], "size": [1, 1, 6], "inflate": -0.05, "uv": [0, 12]}]}, {"name": "tail3", "parent": "tail2", "pivot": [0, 8.475, 16.3], "rotation": [-120, 0, 0], "cubes": [{"origin": [-0.5, 7.675, 16.1], "size": [1, 1, 3], "inflate": -0.1, "uv": [0, 0]}]}, {"name": "leg0", "parent": "capuchin_monkey", "pivot": [1.5, 6, -3.2], "cubes": [{"origin": [1, 3, -3.5], "size": [1, 3, 1], "inflate": 0.1, "pivot": [1.5, 4.5, -2.8], "rotation": [7.5, 0, 0], "uv": [20, 20]}]}, {"name": "leg0_0", "parent": "leg0", "pivot": [1.5, 3, -2.8], "cubes": [{"origin": [1, 0, -3.3], "size": [1, 3, 1], "uv": [16, 20]}]}, {"name": "leg2", "parent": "capuchin_monkey", "pivot": [1.5, 6, 3.3], "cubes": [{"origin": [1, 3, 3], "size": [1, 3, 1], "inflate": 0.1, "pivot": [1.5, 4.5, 3.7], "rotation": [7.5, 0, 0], "uv": [19, 15]}]}, {"name": "leg0_2", "parent": "leg2", "pivot": [1.5, 3, 3.7], "cubes": [{"origin": [1, 0, 3.2], "size": [1, 3, 1], "uv": [4, 19]}]}, {"name": "leg3", "parent": "capuchin_monkey", "pivot": [-1.5, 6, 3.3], "cubes": [{"origin": [-2, 3, 3], "size": [1, 3, 1], "inflate": 0.1, "pivot": [-1.5, 4.5, 3.7], "rotation": [7.5, 0, 0], "uv": [0, 19]}]}, {"name": "leg0_3", "parent": "leg3", "pivot": [-1.5, 3, 3.7], "cubes": [{"origin": [-2, 0, 3.2], "size": [1, 3, 1], "uv": [16, 12]}]}, {"name": "leg1", "parent": "capuchin_monkey", "pivot": [-1.5, 6, -3.2], "cubes": [{"origin": [-2, 3, -3.5], "size": [1, 3, 1], "inflate": 0.1, "pivot": [-1.5, 4.5, -2.8], "rotation": [7.5, 0, 0], "uv": [12, 20]}]}, {"name": "leg0_1", "parent": "leg1", "pivot": [-1.5, 3, -2.8], "cubes": [{"origin": [-2, 0, -3.3], "size": [1, 3, 1], "uv": [8, 20]}]}, {"name": "root", "pivot": [0, 0, 0]}, {"name": "rotation", "parent": "root", "pivot": [0, 0, 0]}, {"name": "waist2", "parent": "rotation", "pivot": [0, 12, 0]}, {"name": "body_human", "parent": "waist2", "pivot": [0, 24, 0]}, {"name": "leftArm", "parent": "body_human", "pivot": [5, 22, 0]}, {"name": "la", "parent": "leftArm", "pivot": [6, 18, 0]}, {"name": "leftItem", "parent": "la", "pivot": [6, 15, 1]}, {"name": "rightArm", "parent": "body_human", "pivot": [-5, 22, 0]}, {"name": "ra", "parent": "rightArm", "pivot": [-6, 18, 0]}, {"name": "rightItem", "parent": "ra", "pivot": [-6, 17, 2]}]}]}