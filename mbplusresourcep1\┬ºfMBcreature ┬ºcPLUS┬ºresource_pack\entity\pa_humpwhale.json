{"format_version": "1.8.0", "minecraft:client_entity": {"description": {"identifier": "pa:humpwhale", "materials": {"default": "turtle"}, "textures": {"default": "textures/entity/pamobile/pa_humpwhale"}, "geometry": {"default": "geometry.pa_humpwhale"}, "scripts": {"pre_animation": ["variable.timeMultiplier = query.has_rider ? 0.39972 : 1.0;", "variable.backLegMultiplier = query.has_rider ? 0.5 : 3.0;", "variable.frontLegMultiplier = query.has_rider ? 2.0 : 8.0;", "variable.legSpeedMultiplier = query.has_rider ? 2.0 : 5.0;"], "scale": "1.2"}, "animations": {"general": "animation.pa_humpwhale.general", "move": "animation.pa_humpwhale.move", "ground_move": "animation.pa_humpwhale.ground_move", "look_at_target": "animation.pa_humpwhale.look_at_target"}, "animation_controllers": [{"general": "controller.animation.turtle.general"}, {"move": "controller.animation.turtle.move"}], "render_controllers": ["controller.render.turtle"], "spawn_egg": {"texture": "pa:humpwhale", "texture_index": 0}}}}