{
  "format_version": "1.13.0",
  "minecraft:entity": {
    "description": {
      "identifier": "worldanimals:asian_elephant",
      "is_spawnable": true,
      "is_summonable": true,
      "is_experimental": false
    },

    "component_groups": {
      "minecraft:add_saddle": {
        "minecraft:interact": {
          "interactions": [
            {
              "play_sounds": "armor.equip_generic",
              "on_interact": {
                "filters": {
                  "all_of": [
                    { "test" :  "is_family", "subject" : "other", "value" :  "player"},
                    { "test" :  "has_equipment", "domain": "hand","subject" : "other", "value" :  "worldanimals:elephant_saddle"}
                  ]
                },
                "event": "minecraft:on_saddle",
                "target": "self"
              },
              "use_item": true,
              "interact_text": "action.interact.attachchest"
            }
          ]
        }
      },
      "minecraft:add_armor": {
		"minecraft:variant": { "value": 0 },
		"minecraft:fire_immune": false
      },
      "minecraft:ruby_armor": {
      "minecraft:attack": {
        "damage": 20
      },
        "minecraft:health": {
          "value": 100,
          "max": 100
        },
        "minecraft:loot": {
          "table": "loot_tables/entities/elephant/ruby_loot.json"
        },
        "minecraft:interact": {
          "interactions": [
            {
              "on_interact": {
                "filters": { "test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:shears"},
                "event": "minecraft:removearmor"
              },
              "spawn_items": {
                "table": "loot_tables/entities/elephant/ruby.json"
              },
              "interact_text": "action.interact.unequip",
              "use_item": false,
              "hurt_item": 2,
              "play_sounds": "armor.equip_generic"
            }
          ]
        },
        "minecraft:variant": {
          "value": 1
        }
      },
      "minecraft:amethyst_armor": {
      "minecraft:attack": {
        "damage": 24
      },
        "minecraft:health": {
          "value": 100,
          "max": 100
        },
        "minecraft:loot": {
          "table": "loot_tables/entities/elephant/amethyst_loot.json"
        },
        "minecraft:interact": {
          "interactions": [
            {
              "on_interact": {
                "filters": { "test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:shears"},
                "event": "minecraft:removearmor"
              },
              "spawn_items": {
                "table": "loot_tables/entities/elephant/amethyst.json"
              },
              "interact_text": "action.interact.unequip",
              "use_item": false,
              "hurt_item": 2,
              "play_sounds": "armor.equip_generic"
            }
          ]
        },
        "minecraft:variant": {
          "value": 2
        }
      },
      "minecraft:citrine_armor": {
      "minecraft:attack": {
        "damage": 28
      },
        "minecraft:health": {
          "value": 110,
          "max": 110
        },
        "minecraft:loot": {
          "table": "loot_tables/entities/elephant/citrine_loot.json"
        },
        "minecraft:interact": {
          "interactions": [
            {
              "on_interact": {
                "filters": { "test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:shears"},
                "event": "minecraft:removearmor"
              },
              "spawn_items": {
                "table": "loot_tables/entities/elephant/citrine.json"
              },
              "interact_text": "action.interact.unequip",
              "use_item": false,
              "hurt_item": 2,
              "play_sounds": "armor.equip_generic"
            }
          ]
        },
        "minecraft:variant": {
          "value": 3
        }
      },
      "minecraft:diamond_armor": {
      "minecraft:attack": {
        "damage": 25
      },
        "minecraft:health": {
          "value": 100,
          "max": 100
        },
        "minecraft:loot": {
          "table": "loot_tables/entities/elephant/diamond_loot.json"
        },
        "minecraft:interact": {
          "interactions": [
            {
              "on_interact": {
                "filters": { "test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:shears"},
                "event": "minecraft:removearmor"
              },
              "spawn_items": {
                "table": "loot_tables/entities/elephant/diamond.json"
              },
              "interact_text": "action.interact.unequip",
              "use_item": false,
              "hurt_item": 2,
              "play_sounds": "armor.equip_generic"
            }
          ]
        },
        "minecraft:variant": {
          "value": 4
        }
      },
      "minecraft:emerald_armor": {
      "minecraft:attack": {
        "damage": 24
      },
        "minecraft:health": {
          "value": 120,
          "max": 120
        },
        "minecraft:loot": {
          "table": "loot_tables/entities/elephant/emerald_loot.json"
        },
        "minecraft:interact": {
          "interactions": [
            {
              "on_interact": {
                "filters": { "test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:shears"},
                "event": "minecraft:removearmor"
              },
              "spawn_items": {
                "table": "loot_tables/entities/elephant/emerald.json"
              },
              "interact_text": "action.interact.unequip",
              "use_item": false,
              "hurt_item": 2,
              "play_sounds": "armor.equip_generic"
            }
          ]
        },
        "minecraft:variant": {
          "value": 5
        }
      },
      "minecraft:golden_armor": {
      "minecraft:attack": {
        "damage": 16
      },
        "minecraft:health": {
          "value": 100,
          "max": 100
        },
        "minecraft:loot": {
          "table": "loot_tables/entities/elephant/golden_loot.json"
        },
        "minecraft:interact": {
          "interactions": [
            {
              "on_interact": {
                "filters": { "test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:shears"},
                "event": "minecraft:removearmor"
              },
              "spawn_items": {
                "table": "loot_tables/entities/elephant/golden.json"
              },
              "interact_text": "action.interact.unequip",
              "use_item": false,
              "hurt_item": 2,
              "play_sounds": "armor.equip_generic"
            }
          ]
        },
        "minecraft:variant": {
          "value": 6
        }
      },
      "minecraft:iron_armor": {
      "minecraft:attack": {
        "damage": 20
      },
        "minecraft:health": {
          "value": 100,
          "max": 100
        },
        "minecraft:loot": {
          "table": "loot_tables/entities/elephant/iron_loot.json"
        },
        "minecraft:interact": {
          "interactions": [
            {
              "on_interact": {
                "filters": { "test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:shears"},
                "event": "minecraft:removearmor"
              },
              "spawn_items": {
                "table": "loot_tables/entities/elephant/iron.json"
              },
              "interact_text": "action.interact.unequip",
              "use_item": false,
              "hurt_item": 2,
              "play_sounds": "armor.equip_generic"
            }
          ]
        },
        "minecraft:variant": {
          "value": 7
        }
      },
      "minecraft:netherite_armor": {
      "minecraft:attack": {
        "damage": 20
      },
	  "minecraft:fire_immune": true,
        "minecraft:health": {
          "value": 100,
          "max": 100
        },
        "minecraft:loot": {
          "table": "loot_tables/entities/elephant/netherite_loot.json"
        },
        "minecraft:interact": {
          "interactions": [
            {
              "on_interact": {
                "filters": { "test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:shears"},
                "event": "minecraft:removearmor"
              },
              "spawn_items": {
                "table": "loot_tables/entities/elephant/netherite.json"
              },
              "interact_text": "action.interact.unequip",
              "use_item": false,
              "hurt_item": 2,
              "play_sounds": "armor.equip_generic"
            }
          ]
        },
        "minecraft:variant": {
          "value": 8
        }
      },
      "minecraft:reptil_armor": {
      "minecraft:attack": {
        "damage": 20
      },
        "minecraft:health": {
          "value": 100,
          "max": 100
        },
        "minecraft:loot": {
          "table": "loot_tables/entities/elephant/reptil_loot.json"
        },
        "minecraft:interact": {
          "interactions": [
            {
              "on_interact": {
                "filters": { "test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:shears"},
                "event": "minecraft:removearmor"
              },
              "spawn_items": {
                "table": "loot_tables/entities/elephant/reptil.json"
              },
              "interact_text": "action.interact.unequip",
              "use_item": false,
              "hurt_item": 2,
              "play_sounds": "armor.equip_generic"
            }
          ]
        },
        "minecraft:variant": {
          "value": 9
        }
      },
      "minecraft:mooberry_become_cow": {
				"minecraft:instant_despawn": {
					"remove_child_entities": true
				}
      },
      "minecraft:asian_elephant_baby": {
        "minecraft:tameable": {
          "probability": 1.0,
          "tame_items": "worldanimals:collar",
          "tame_event": {
            "event": "minecraft:on_tame",
            "target": "self"
          }
        },
        "minecraft:is_baby": {
        },
        "minecraft:scale": {
          "value": 0.75
        },
        "minecraft:ageable": {
          "duration": 1200,
          "feed_items": [
            {
              "item": "sugar",
              "growth": 0.025
            },
            {
              "item": "worldanimals:sugar_cubes",
              "growth": 0.1
            }
          ],
          "grow_up": {
            "event": "minecraft:ageable_grow_up",
            "target": "self"
          }
        },

        "minecraft:behavior.follow_parent": {
          "priority": 5,
          "speed_multiplier": 1.0
        }
      },

      "minecraft:asian_elephant_adult": {
        "minecraft:tameable": {
          "probability": 1.0,
          "tame_items": "worldanimals:collar",
          "tame_event": {
            "event": "minecraft:on_tame",
            "target": "self"
          }
        },
        "minecraft:scale": {
          "value": 1.75
        },
        "minecraft:behavior.nearest_attackable_target": {
          "priority": 4,
          "attack_interval": 10,
          "reselect_targets": true,
          "entity_types": [
            {
              "filters": {
                "any_of": [
                  { "test" :  "is_family", "subject" : "other", "value" :  "skeleton"},
                  { "test" :  "is_family", "subject" : "other", "value" :  "zombie"}
                ] 
              },
              "max_dist": 16
            },
            {
              "filters": {
                "all_of": [
                  {
                    "test": "is_family",
                    "subject": "other",
                    "value": "baby_turtle"
                  },
                  {
                    "test": "in_water",
                    "subject": "other",
                    "operator": "!=",
                    "value": true
                  }
                ]
              },
              "max_dist": 16
            },
            {
              "filters": {
                "all_of": [
                  {
                    "test": "is_family",
                    "subject": "other",
                    "value": "skeleton"
                  },
                  {
                    "test": "is_underwater",
                    "subject": "other",
                    "operator": "!=",
                    "value": true
                  }
                ]
              },
              "max_dist": 16
            }
          ],
          "must_see": true
        },
        "minecraft:experience_reward": {
          "on_bred": "Math.Random(1,7)",
          "on_death": "query.last_hit_by_player ? Math.Random(1,3) : 0"
        },
        "minecraft:collision_box": {
          "width": 0.9,
          "height": 1.87
        },
        "minecraft:behavior.breed": {
          "priority": 4,
          "speed_multiplier": 1.0
        },
        "minecraft:breedable": {
          "require_tame": true,
          "inherit_tamed": false,
          "love_filters": { "test": "is_mark_variant", "subject": "self", "operator": "!=", "value": 1 }, // Wandering Trader asian_elephants can't fall in love
          "breeds_with": {
            "mate_type": "worldanimals:asian_elephant",
            "baby_type": "worldanimals:asian_elephant",
            "breed_event": {
              "event": "minecraft:entity_born",
              "target": "baby"
            }
          },
          "breed_items": [ "hay_block" ]
        }
      },

      "minecraft:asian_elephant_wandering_trader": {
        "minecraft:on_friendly_anger": {
          "event": "minecraft:defend_wandering_trader",
          "target": "self"
        }, 
        "minecraft:environment_sensor": {
          // If this is a Wandering Trader's asian_elephant and it was just released from its leash, make it tame
          "triggers": {
            "filters": {
              "all_of": [
                { "test": "is_leashed", "subject": "self", "value": false },
                { "test": "has_component", "subject": "self", "operator": "!=", "value" :  "minecraft:is_tamed" }
              ]
            },
            "event": "minecraft:on_tame"
          }
        }
      },

      "minecraft:strength_1": {
        "minecraft:strength": {
          "value": 1,
          "max": 5
        }
      },
      "minecraft:strength_2": {
        "minecraft:strength": {
          "value": 2,
          "max": 5
        }
      },
      "minecraft:strength_3": {
        "minecraft:strength": {
          "value": 3,
          "max": 5
        }
      },
      "minecraft:strength_4": {
        "minecraft:strength": {
          "value": 4,
          "max": 5
        }
      },
      "minecraft:strength_5": {
        "minecraft:strength": {
          "value": 5,
          "max": 5
        }
      },

      "minecraft:asian_elephant_wild": {
	  
      },


      "minecraft:asian_elephant_tamed": {
        "minecraft:behavior.owner_hurt_by_target": {
          "priority": 1
        },
        "minecraft:interact": {
          "interactions": [
            {
              "play_sounds": "armor.equip_generic",
              "on_interact": {
                "filters": {
                  "all_of": [
                    { "test" :  "is_family", "subject" : "other", "value" :  "player"},
                    { "test" :  "has_equipment", "domain": "hand","subject" : "other", "value" :  "worldanimals:elephant_saddle"}
                  ]
                },
                "event": "minecraft:on_saddle",
                "target": "self"
              },
              "use_item": true,
              "interact_text": "action.interact.attachchest"
            }
          ]
        },
        "minecraft:behavior.owner_hurt_target": {
          "priority": 2
        },
        "minecraft:is_tamed": {
        },
        "minecraft:sittable": {
        },
      "minecraft:behavior.stay_while_sitting": {
        "priority": 3
      },
        "minecraft:behavior.follow_owner": {
          "priority": 6,
          "speed_multiplier": 1.0,
          "start_distance": 12,
          "stop_distance": 3
        },
        "minecraft:rideable": {
          "priority": 0,
          "seat_count": 1,
          "crouching_skip_interact": true,
          "family_types": [
            "player"
          ],
          "interact_text": "action.interact.ride.horse",
        "seats": [
          {
         "position": [ 0.0, 1.9, -0.15 ],
         "min_rider_count": 0,
         "max_rider_count": 7
          },
          {
           "position": [ 0.0, 1.9, -0.35 ],
         "min_rider_count": 1,
         "max_rider_count": 7
          }
        ]

        }
      },

      "minecraft:asian_elephant_saddle": {
	          "minecraft:behavior.player_ride_tamed": {
        },
	  "minecraft:inventory": {
        "inventory_size": 54,
        "container_type": "container", 
        "private": false
      },
        "minecraft:input_ground_controlled": {
        },
        "minecraft:can_power_jump": {
        },
        "minecraft:is_saddled": {

        },
        "minecraft:loot": {
          "table": "loot_tables/entities/elephant_saddle.json"
        }
      },

      "minecraft:in_caravan": {
        "minecraft:damage_sensor": {
          "triggers": {
            "cause": "all",
            "deals_damage": true
          }
        }
      }

    },


    "components": {
      "minecraft:type_family": {
        "family": [ "asian_elephant", "animal" ]
      },
      "minecraft:breathable": {
        "total_supply": 15,
        "suffocate_time": 0
      },
      "minecraft:nameable": {
      },
      "minecraft:health": {
        "value": {
          "range_min": 70,
          "range_max": 70
        }
      },
        "minecraft:interact": {
          "interactions": [
				{
					"on_interact": {
						"event": "become_cow",
						"target": "self",
						"filters": {
							"all_of": [
								{
									"test": "has_equipment",
									"domain": "hand",
									"subject": "other",
									"value": "worldanimals:mammoth_dna"
								}
							]
						}
					},
					"use_item": true,
					"interact_text": "UPGRADE"
				},
            {
              "on_interact": {
                "filters": {
                  "all_of": [
                    { "test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:syringe"},
                    { "test" :  "is_family", "subject" : "other", "value" :  "player"}
                  ]
                }
              },
              "use_item": true,
              "transform_to_item": "worldanimals:elephant_dna",
              "play_sounds": "milk",
              "interact_text": "action.interact.milk"
            }
          ]
        },
      "minecraft:attack": {
        "damage": 7
      },
        "minecraft:behavior.nearest_attackable_target": {
          "priority": 5,
          "attack_interval": 10,
          "entity_types": [
            {
              "filters": { "test" :  "is_family", "subject" : "other", "value" :  "skeleton"},
              "max_dist": 16
            }
          ],
          "must_see": true
        },
      "minecraft:behavior.melee_attack": {
        "priority": 5,
        "target_dist": 1.2,
        "track_target": true,
        "reach_multiplier": 1.0
      },
      "minecraft:hurt_on_condition": {
        "damage_conditions": [
          {
            "filters": { "test": "in_lava", "subject": "self", "operator": "==", "value": true },
            "cause": "lava",
            "damage_per_tick": 4
          }
        ]
      },
      "minecraft:movement": {
        "value": 0.2
      },
      "minecraft:navigation.walk": {
        "can_path_over_water": true,
        "avoid_damage_blocks": true
      },
      "minecraft:movement.basic": {
      },
      "minecraft:jump.static": {
      },
      "minecraft:follow_range": {
        "value": 40,
        "max": 40
      },
      "minecraft:leashable": {
        "soft_distance": 4.0,
        "hard_distance": 6.0,
        "max_distance": 10.0,
        "can_be_stolen": true
      },
      "minecraft:balloonable": {
      },
      "minecraft:healable": {
        "items": [
          {
            "item": "sugar",
            "heal_amount": 1.5
          },
          {
            "item": "worldanimals:sugar_cubes",
            "heal_amount": 6
          }
        ]
      },

      "minecraft:behavior.float": {
        "priority": 0
      },
      "minecraft:celebrate": {
        "minecraft:behavior.celebrate": {
          "priority": 5,
          "celebration_sound": "celebrate",
          "sound_interval": {
            "range_min": 2.0,
            "range_max": 7.0
          },
          "jump_interval": {
            "range_min": 1.0,
            "range_max": 3.5
          },
          "duration": 30.0,
          "on_celebration_end_event": {
            "event": "minecraft:stop_celebrating",
            "target": "self"
          }
        }
      },
        "minecraft:celebrate_hunt": {
          "celebration_targets": {
            "all_of": [
              {
                "test": "is_family",
                "value": "monster"
              }
            ]
          },
          "broadcast": true,
          "duration": 10,
          "celebrate_sound": "celebrate",
          "sound_interval": {
            "range_min": 2.0,
            "range_max": 5.0
          },
          "radius": 16
        },
      "minecraft:behavior.run_around_like_crazy": {
        "priority": 1,
        "speed_multiplier": 1.2
      },
      "minecraft:behavior.random_stroll": {
        "priority": 6,
        "speed_multiplier": 0.7
      },
      "minecraft:behavior.look_at_player": {
        "priority": 7,
        "look_distance": 6.0,
        "probability": 0.02
      },
      "minecraft:behavior.random_look_around": {
        "priority": 8
      },
        "minecraft:tameable": {
          "probability": 1.0,
          "tame_items": "worldanimals:collar",
          "tame_event": {
            "event": "minecraft:on_tame",
            "target": "self"
          }
        },
      "minecraft:behavior.mount_pathing": {
        "priority": 1,
        "speed_multiplier": 1.25,
        "target_dist": 0.0,
        "track_target": true
      },
      "minecraft:behavior.hurt_by_target": {
        "priority": 1,
        "hurt_owner": true
      },
      "minecraft:behavior.nearest_attackable_target": {
        "priority": 2,
        "attack_interval": 16,
        "entity_types": [
          {
            "filters": {
                "all_of": [
                  { "test" :  "is_family", "subject" : "other", "value" :  "wolf"},
                  { "test" :  "has_component","subject" : "other", "operator": "!=", "value" :  "minecraft:is_tamed"}
                ]
              },
            "max_dist": 10
          }
        ],
        "must_see": false,
        "must_reach": true
      },
      "minecraft:on_target_acquired": {
        "filters": {
                "all_of": [
                  { "test" :  "is_family", "subject" : "target", "value" :  "wolf"},
                  { "test" :  "has_component","subject" : "target", "operator": "!=", "value" :  "minecraft:is_tamed"}
                ]
              },
        "event": "minecraft:mad_at_wolf",
        "target": "self"
      },
      "minecraft:on_target_escape": {
        "filters": {
                "all_of": [
                  { "test" :  "is_family", "subject" : "target", "value" :  "wolf"},
                  { "test" :  "has_component","subject" : "target", "operator": "!=", "value" :  "minecraft:is_tamed"}
                ]
              },
        "event": "minecraft:on_calm",
        "target": "self"
      },
	    "minecraft:physics": {
      },
      "minecraft:pushable": {
        "is_pushable": true,
        "is_pushable_by_piston": true
      }
    },


    "events": {
      "become_cow": {
        "remove": {
        },
        "add": {
          "component_groups": [
            "minecraft:mooberry_become_cow"
          ]
        },
         "run_command": {
              "command": [
                "summon worldanimals:mammoth ~ ~ ~ minecraft:entity_spawned"
             ]
          }
      },
      "minecraft:entity_spawned": {
        "sequence": [
          {
            "randomize": [
              {
                "weight": 90,
                "remove": {
                },
                "add": {
                  "component_groups": [
                    "minecraft:asian_elephant_adult",
                    "minecraft:asian_elephant_wild"
                  ]
                }
              },
              {
                "weight": 10,
                "remove": {
                },
                "add": {
                  "component_groups": [
                    "minecraft:asian_elephant_baby"
                  ]

                }
              }
            ]
          },
          {
            "randomize": [
              {
                "weight": 32,
                "add": {
                  "component_groups": [
                    "minecraft:strength_1"
                  ]
                }
              },
              {
                "weight": 32,
                "add": {
                  "component_groups": [
                    "minecraft:strength_2"
                  ]
                }
              },
              {
                "weight": 32,
                "add": {
                  "component_groups": [
                    "minecraft:strength_3"
                  ]
                }
              },
              {
                "weight": 2,
                "add": {
                  "component_groups": [
                    "minecraft:strength_4"
                  ]
                }
              },
              {
                "weight": 2,
                "add": {
                  "component_groups": [
                    "minecraft:strength_5"
                  ]
                }
              }
            ]
          },
          {
            "randomize": [
              {
                "weight": 25,
                "add": {
                  "component_groups": [
                    "minecraft:asian_elephant_creamy"
                  ]
                }
              },
              {
                "weight": 25,
                "add": {
                  "component_groups": [
                    "minecraft:asian_elephant_white"
                  ]
                }
              },
              {
                "weight": 25,
                "add": {
                  "component_groups": [
                    "minecraft:asian_elephant_brown"
                  ]
                }
              },
              {
                "weight": 25,
                "add": {
                  "component_groups": [
                    "minecraft:asian_elephant_gray"
                  ]
                }
              }
            ]
          }
        ]
      },

      "minecraft:entity_born": {
        "add": {
          "component_groups": [
            "minecraft:asian_elephant_baby"
          ]
        }
      },

      "minecraft:from_wandering_trader": {
        "sequence": [
          {
            "add": {
              "component_groups": [
                "minecraft:asian_elephant_adult",
                "minecraft:asian_elephant_wandering_trader"
              ]
            }
          },
          {
            "randomize": [
              {
                "weight": 32,
                "add": {
                  "component_groups": [
                    "minecraft:strength_1"
                  ]
                }
              },
              {
                "weight": 32,
                "add": {
                  "component_groups": [
                    "minecraft:strength_2"
                  ]
                }
              },
              {
                "weight": 32,
                "add": {
                  "component_groups": [
                    "minecraft:strength_3"
                  ]
                }
              },
              {
                "weight": 2,
                "add": {
                  "component_groups": [
                    "minecraft:strength_4"
                  ]
                }
              },
              {
                "weight": 2,
                "add": {
                  "component_groups": [
                    "minecraft:strength_5"
                  ]
                }
              }
            ]
          },
          {
            "randomize": [
              {
                "weight": 25,
                "add": {
                  "component_groups": [
                    "minecraft:asian_elephant_creamy"
                  ]
                }
              },
              {
                "weight": 25,
                "add": {
                  "component_groups": [
                    "minecraft:asian_elephant_white"
                  ]
                }
              },
              {
                "weight": 25,
                "add": {
                  "component_groups": [
                    "minecraft:asian_elephant_brown"
                  ]
                }
              },
              {
                "weight": 25,
                "add": {
                  "component_groups": [
                    "minecraft:asian_elephant_gray"
                  ]
                }
              }
            ]
          }
        ]
      },

      "minecraft:ageable_grow_up": {
        "remove": {
          "component_groups": [
            "minecraft:asian_elephant_baby"
          ]
        },
        "add": {
          "component_groups": [
            "minecraft:asian_elephant_adult",
            "minecraft:asian_elephant_wild"
          ]
        }
      },

      "minecraft:on_tame": {
        "remove": {
          "component_groups": [
            "minecraft:asian_elephant_wild"
          ]
        },
        "add": {
          "component_groups": [
            "minecraft:asian_elephant_tamed",
			"minecraft:add_saddle"
          ]
        }
      },
      "minecraft:join_caravan": {
        "add": {
          "component_groups": [
            "minecraft:in_caravan"
          ]
        }
      },
      "minecraft:leave_caravan": {
        "remove": {
          "component_groups": [
            "minecraft:in_caravan"
          ]
        }
      },

      "minecraft:on_saddle": {
        "add": {
          "component_groups": [
            "minecraft:asian_elephant_saddle",
			"minecraft:add_armor"
          ]
        }
      },

      "minecraft:off_saddle": {
        "remove": {
          "component_groups": [
            "minecraft:asian_elephant_saddle"
          ]
        }
      },

      "minecraft:amethyst_armor": {
        "remove": {
          "component_groups": [
            "minecraft:add_armor"
          ]
        },
        "add": {
          "component_groups": [
            "minecraft:amethyst_armor"
          ]
        }
      },
      "minecraft:ruby_armor": {
        "remove": {
          "component_groups": [
            "minecraft:add_armor"
          ]
        },
        "add": {
          "component_groups": [
            "minecraft:ruby_armor"
          ]
        }
      },
      "minecraft:diamond_armor": {
        "remove": {
          "component_groups": [
            "minecraft:add_armor"
          ]
        },
        "add": {
          "component_groups": [
            "minecraft:diamond_armor"
          ]
        }
      },
      "minecraft:emerald_armor": {
        "remove": {
          "component_groups": [
            "minecraft:add_armor"
          ]
        },
        "add": {
          "component_groups": [
            "minecraft:emerald_armor"
          ]
        }
      },
      "minecraft:iron_armor": {
        "remove": {
          "component_groups": [
            "minecraft:add_armor"
          ]
        },
        "add": {
          "component_groups": [
            "minecraft:iron_armor"
          ]
        }
      },
      "minecraft:golden_armor": {
        "remove": {
          "component_groups": [
            "minecraft:add_armor"
          ]
        },
        "add": {
          "component_groups": [
            "minecraft:golden_armor"
          ]
        }
      },
      "minecraft:reptil_armor": {
        "remove": {
          "component_groups": [
            "minecraft:add_armor"
          ]
        },
        "add": {
          "component_groups": [
            "minecraft:reptil_armor"
          ]
        }
      },
      "minecraft:citrine_armor": {
        "remove": {
          "component_groups": [
            "minecraft:add_armor"
          ]
        },
        "add": {
          "component_groups": [
            "minecraft:citrine_armor"
          ]
        }
      },
      "minecraft:emerald_armor": {
        "remove": {
          "component_groups": [
            "minecraft:add_armor"
          ]
        },
        "add": {
          "component_groups": [
            "minecraft:emerald_armor"
          ]
        }
      },
      "minecraft:netherite_armor": {
        "remove": {
          "component_groups": [
            "minecraft:add_armor"
          ]
        },
        "add": {
          "component_groups": [
            "minecraft:netherite_armor"
          ]
        }
      },
      "minecraft:removearmor": {
        "add": {
          "component_groups": [
            "minecraft:add_armor"
          ]
        },
        "remove": {
          "component_groups": [
            "minecraft:ruby_armor",
			"minecraft:amethyst_armor",
			"minecraft:diamond_armor",
			"minecraft:golden_armor",
			"minecraft:iron_armor",
			"minecraft:reptil_armor",
			"minecraft:netherite_armor",
			"minecraft:emerald_armor",
			"minecraft:citrine_armor"
          ]
        }
      }

    }
  }
}
