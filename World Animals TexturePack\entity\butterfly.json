{"format_version": "1.8.0", "minecraft:client_entity": {"description": {"identifier": "worldanimals:butterfly", "materials": {"default": "parrot"}, "textures": {"blue": "textures/entity/butterfly_v2/butterfly0", "green": "textures/entity/butterfly_v2/butterfly1", "red_blue": "textures/entity/butterfly_v2/butterfly2", "yellow_blue": "textures/entity/butterfly_v2/butterfly3", "grey": "textures/entity/butterfly_v2/butterfly4"}, "geometry": {"default": "geometry.butterfly_v2"}, "scripts": {"pre_animation": ["variable.state = query.is_dancing ? 3 : (query.is_sitting ? 2 : (!query.is_on_ground && !query.is_jumping && !query.is_riding ? 0 : 1));", "variable.dance.x = Math.cos(query.life_time * 57.3 * 20.0);", "variable.dance.y = -Math.sin(query.life_time * 57.3 * 20.0);", "variable.wing_flap = ((math.sin(query.wing_flap_position * 57.3) + 1) * query.wing_flap_speed);"]}, "animations": {"moving": "animation.parrot.moving", "base": "animation.parrot.base", "dance": "animation.parrot.dance", "sitting": "animation.parrot.sitting", "flying": "animation.butterfly_v2.idle", "standing": "animation.parrot.standing", "look_at_target": "animation.common.look_at_target"}, "animation_controllers": [{"setup": "controller.animation.parrot.setup"}, {"move": "controller.animation.parrot.move"}], "render_controllers": ["controller.render.parrot"], "spawn_egg": {"texture": "egg_butterfly", "texture_index": 0}}}}