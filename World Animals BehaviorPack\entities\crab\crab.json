{"format_version": "1.14.0", "minecraft:entity": {"description": {"identifier": "worldanimals:crab", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"minecraft:parrot_red": {"minecraft:variant": {"value": 0}}, "minecraft:parrot_blue": {"minecraft:variant": {"value": 1}}, "minecraft:baby": {"minecraft:is_baby": {}, "minecraft:scale": {"value": 0.5}, "minecraft:ageable": {"duration": 1200, "grow_up": {"event": "minecraft:ageable_grow_up", "target": "self"}}, "minecraft:behavior.follow_parent": {"priority": 4, "speed_multiplier": 1.25}}, "minecraft:baby_wild": {"minecraft:on_target_acquired": {"event": "minecraft:on_scared", "target": "self"}, "minecraft:behavior.nearest_attackable_target": {"priority": 4, "entity_types": [{"filters": {"test": "is_family", "subject": "other", "value": "player"}, "max_dist": 16}], "must_see": false}}, "minecraft:baby_scared": {"minecraft:angry": {"duration": 1, "broadcast_anger": true, "broadcast_range": 41, "calm_event": {"event": "minecraft:baby_on_calm", "target": "self"}}}, "minecraft:adult": {"minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? Math.Random(1,3) : 0"}, "minecraft:loot": {"table": "loot_tables/entities/crab.json"}}, "minecraft:adult_wild": {"minecraft:on_target_acquired": {"event": "minecraft:on_anger", "target": "self"}, "minecraft:on_friendly_anger": {"event": "minecraft:on_anger", "target": "self"}, "minecraft:behavior.nearest_attackable_target": {"priority": 4, "entity_types": [{"filters": {"test": "is_family", "subject": "other", "value": "fox"}, "max_dist": 16}], "must_see": false}}, "minecraft:adult_hostile": {"minecraft:attack": {"damage": 6.0}, "minecraft:angry": {"duration": 500, "broadcast_anger": false, "broadcast_range": 20, "calm_event": {"event": "minecraft:on_calm", "target": "self"}}, "minecraft:behavior.stomp_attack": {"priority": 1, "track_target": true, "require_complete_path": true}}}, "components": {"minecraft:type_family": {"family": ["crab", "animal"]}, "minecraft:breathable": {"total_supply": 15, "suffocate_time": 0}, "minecraft:nameable": {}, "minecraft:health": {"value": 30}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:collision_box": {"width": 0.85, "height": 0.85}, "minecraft:movement": {"value": 0.25}, "minecraft:water_movement": {"drag_factor": 0.98}, "minecraft:navigation.climb": {"can_path_over_water": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:follow_range": {"value": 48}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.hurt_by_target": {"priority": 1}, "minecraft:behavior.random_stroll": {"priority": 5}, "minecraft:behavior.look_at_player": {"priority": 6, "target_distance": 6.0, "probability": 0.02}, "minecraft:behavior.random_look_around": {"priority": 7}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:behavior.panic": {"priority": 2, "speed_multiplier": 2.0}, "minecraft:leashable": {"soft_distance": 4.0, "hard_distance": 6.0, "max_distance": 10.0}}, "events": {"minecraft:entity_spawned": {"randomize": [{"weight": 4, "add": {"component_groups": ["minecraft:adult_hostile", "minecraft:adult", "minecraft:parrot_red", "minecraft:adult_wild"]}}, {"weight": 1, "add": {"component_groups": ["minecraft:baby", "minecraft:parrot_red", "minecraft:baby_wild"]}}, {"weight": 4, "add": {"component_groups": ["minecraft:adult_hostile", "minecraft:parrot_blue", "minecraft:adult", "minecraft:adult_wild"]}}, {"weight": 1, "add": {"component_groups": ["minecraft:baby", "minecraft:parrot_blue", "minecraft:baby_wild"]}}]}, "minecraft:entity_born": {"add": {"component_groups": ["minecraft:baby", "minecraft:baby_wild"]}}, "minecraft:ageable_grow_up": {"remove": {"component_groups": ["minecraft:baby", "minecraft:baby_wild", "minecraft:baby_scared"]}, "add": {"component_groups": ["minecraft:adult", "minecraft:adult_wild"]}}, "minecraft:on_calm": {"remove": {"component_groups": ["minecraft:adult_hostile"]}, "add": {"component_groups": ["minecraft:adult_wild"]}}, "minecraft:on_anger": {"remove": {"component_groups": ["minecraft:adult_wild"]}, "add": {"component_groups": ["minecraft:adult_hostile"]}}, "minecraft:baby_on_calm": {"remove": {"component_groups": ["minecraft:baby_scared"]}, "add": {"component_groups": ["minecraft:baby_wild"]}}, "minecraft:on_scared": {"remove": {"component_groups": ["minecraft:baby_wild"]}, "add": {"component_groups": ["minecraft:baby_scared"]}}}}}