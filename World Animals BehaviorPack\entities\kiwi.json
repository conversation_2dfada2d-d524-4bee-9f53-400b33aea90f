{"format_version": "1.13.0", "minecraft:entity": {"description": {"identifier": "worldanimals:kiwi", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"minecraft:kiwi_baby": {"minecraft:ageable": {"duration": 1200, "feed_items": ["fish", "salmon"], "grow_up": {"event": "minecraft:ageable_grow_up", "target": "self"}}, "minecraft:is_baby": {}, "minecraft:scale": {"value": 0.5}}, "minecraft:kiwi_adult": {"minecraft:experience_reward": {"on_bred": "<PERSON><PERSON>(1,7)", "on_death": "query.last_hit_by_player ? Math.Random(1,3) : 0"}}, "minecraft:kiwi_angry": {"minecraft:angry": {"duration": 25, "broadcast_anger": true, "broadcast_range": 20, "calm_event": {"event": "minecraft:on_calm", "target": "self"}}, "minecraft:on_target_acquired": {}}, "minecraft:kiwi_wild": {"minecraft:behavior.avoid_mob_type": {"priority": 3, "entity_types": [{"filters": {"test": "is_family", "subject": "other", "value": "llama"}, "max_dist": 24, "walk_speed_multiplier": 1.5, "sprint_speed_multiplier": 1.5}], "probability_per_strength": 0.14}, "minecraft:tameable": {"probability": 1.0, "tame_items": "worldanimals:collar", "tame_event": {"event": "minecraft:on_tame", "target": "self"}}, "minecraft:behavior.nearest_attackable_target": {"priority": 4, "attack_interval": 10, "reselect_targets": true, "entity_types": [{"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "skeleton"}, {"test": "is_family", "subject": "other", "value": "sheep"}, {"test": "is_family", "subject": "other", "value": "rabbit"}, {"test": "is_family", "subject": "other", "value": "fox"}]}, "max_dist": 16}, {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "baby_turtle"}, {"test": "in_water", "subject": "other", "operator": "!=", "value": true}]}, "max_dist": 16}, {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "skeleton"}, {"test": "is_underwater", "subject": "other", "operator": "!=", "value": true}]}, "max_dist": 16}], "must_see": true}, "minecraft:on_target_acquired": {"event": "minecraft:become_angry", "target": "self"}, "minecraft:rideable": {"seat_count": 1, "family_types": ["zombie"], "seats": {"position": [0.0, 0.675, -0.1]}}}, "minecraft:kiwi_tame": {"minecraft:is_tamed": {}, "minecraft:health": {"value": 25, "max": 25}, "minecraft:color": {"value": 14}, "minecraft:behavior.follow_owner": {"priority": 6, "speed_multiplier": 1.0, "start_distance": 10, "stop_distance": 2}, "minecraft:attack": {"damage": 3}, "minecraft:behavior.breed": {"priority": 2, "speed_multiplier": 1.0}, "minecraft:behavior.owner_hurt_by_target": {"priority": 1}, "minecraft:behavior.owner_hurt_target": {"priority": 2}, "minecraft:behavior.nearest_attackable_target": {"priority": 5, "attack_interval": 10, "entity_types": [{"filters": {"test": "is_family", "subject": "other", "value": "skeleton"}, "max_dist": 16}], "must_see": true}, "minecraft:sittable": {}, "minecraft:is_dyeable": {"interact_text": "action.interact.dye"}, "minecraft:leashable": {"soft_distance": 4.0, "hard_distance": 6.0, "max_distance": 10.0, "on_leash": {"event": "minecraft:on_leash", "target": "self"}, "on_unleash": {"event": "minecraft:on_unleash", "target": "self"}}}}, "components": {"minecraft:nameable": {}, "minecraft:variant": {"value": 0}, "minecraft:type_family": {"family": ["kiwi", "mob"]}, "minecraft:collision_box": {"width": 0.6, "height": 0.8}, "minecraft:health": {"value": 8, "max": 8}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:movement": {"value": 0.2}, "minecraft:navigation.walk": {"can_path_over_water": true, "avoid_damage_blocks": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:attack": {"damage": 3}, "minecraft:healable": {"items": [{"item": "wheat_seeds", "heal_amount": 2}, {"item": "beetroot_seeds", "heal_amount": 2}, {"item": "melon_seeds", "heal_amount": 2}, {"item": "pumpkin_seeds", "heal_amount": 2}]}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.mount_pathing": {"priority": 1, "speed_multiplier": 1.25, "target_dist": 0, "track_target": true}, "minecraft:behavior.stay_while_sitting": {"priority": 3}, "minecraft:behavior.leap_at_target": {"priority": 4, "target_dist": 0.4}, "minecraft:behavior.melee_attack": {"priority": 5, "target_dist": 1.2, "track_target": true, "reach_multiplier": 1.0}, "minecraft:behavior.random_stroll": {"priority": 8, "speed_multiplier": 1.0}, "minecraft:behavior.look_at_player": {"priority": 6, "target_distance": 6.0, "probability": 0.02}, "minecraft:behavior.beg": {"priority": 9, "look_distance": 8, "look_time": [2, 4], "items": ["bone", "porkchop", "cooked_porkchop", "chicken", "cooked_chicken", "beef", "cooked_beef", "rotten_flesh", "muttonraw", "muttoncooked", "rabbit", "cooked_rabbit"]}, "minecraft:behavior.hurt_by_target": {"priority": 3}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}}, "events": {"minecraft:entity_spawned": {"randomize": [{"weight": 9, "remove": {}, "add": {"component_groups": ["minecraft:kiwi_adult", "minecraft:kiwi_wild"]}}, {"weight": 1, "remove": {}, "add": {"component_groups": ["minecraft:kiwi_baby", "minecraft:kiwi_wild"]}}]}, "minecraft:entity_born": {"remove": {}, "add": {"component_groups": ["minecraft:kiwi_baby", "minecraft:kiwi_tame"]}}, "minecraft:ageable_grow_up": {"remove": {"component_groups": ["minecraft:kiwi_baby"]}, "add": {"component_groups": ["minecraft:kiwi_adult"]}}, "minecraft:ageable_set_baby": {"remove": {"component_groups": ["minecraft:kiwi_adult"]}, "add": {"component_groups": ["minecraft:kiwi_baby"]}}, "minecraft:on_tame": {"remove": {"component_groups": ["minecraft:kiwi_wild"]}, "add": {"component_groups": ["minecraft:kiwi_tame"]}}, "minecraft:become_angry": {"remove": {"component_groups": ["minecraft:kiwi_wild"]}, "add": {"component_groups": ["minecraft:kiwi_angry"]}}, "minecraft:on_calm": {"remove": {"component_groups": ["minecraft:kiwi_angry"]}, "add": {"component_groups": ["minecraft:kiwi_wild"]}}}}}