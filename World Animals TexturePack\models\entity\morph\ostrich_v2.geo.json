{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.ostrich_v2", "texture_width": 128, "texture_height": 128, "visible_bounds_width": 4, "visible_bounds_height": 4.5, "visible_bounds_offset": [0, 1.75, 0]}, "bones": [{"name": "into_morph", "pivot": [0, 0, 0]}, {"name": "ostrich", "parent": "into_morph", "pivot": [0, 0, 0]}, {"name": "leg1", "parent": "ostrich", "pivot": [-3, 13, 0], "cubes": [{"origin": [-5, 10, -1], "size": [3, 6, 4], "inflate": 0.25, "pivot": [0, 13, 1], "rotation": [10, 0, 0], "uv": [12, 55]}]}, {"name": "rodilla2", "parent": "leg1", "pivot": [-3, 10, 1.5], "cubes": [{"origin": [-5, 0.1, -1], "size": [3, 11, 3], "inflate": -0.35, "pivot": [0, 5.5, 0.5], "rotation": [-10, 0, 0], "uv": [0, 55]}, {"origin": [-5, -0.05, -4], "size": [3, 1, 5], "inflate": -0.05, "uv": [54, 21]}]}, {"name": "leg0", "parent": "ostrich", "pivot": [3, 13, 0], "cubes": [{"origin": [2, 10, -1], "size": [3, 6, 4], "inflate": 0.25, "pivot": [0, 13, 1], "rotation": [10, 0, 0], "uv": [54, 38]}]}, {"name": "rodilla0", "parent": "leg0", "pivot": [3, 10, 1.5], "cubes": [{"origin": [2, 0.1, -1], "size": [3, 11, 3], "inflate": -0.35, "pivot": [0, 5.5, 0.5], "rotation": [-10, 0, 0], "uv": [51, 49]}, {"origin": [2, -0.05, -4], "size": [3, 1, 5], "inflate": -0.05, "uv": [43, 20]}]}, {"name": "body", "parent": "ostrich", "pivot": [0, 13, 2], "cubes": [{"origin": [-4, 13, -7], "size": [8, 8, 16], "uv": [0, 0]}]}, {"name": "tail", "parent": "body", "pivot": [0, 20, 9], "rotation": [37.5, 0, 0], "cubes": [{"origin": [-3, 19, 8], "size": [6, 2, 6], "inflate": -0.25, "uv": [12, 38]}, {"origin": [-2, 19, 13.45], "size": [4, 2, 2], "inflate": -0.275, "uv": [28, 24]}]}, {"name": "montura", "parent": "body", "pivot": [4, 21, -2], "cubes": [{"origin": [-5, 21, -5], "size": [10, 1, 8], "uv": [28, 29]}, {"origin": [-4, 21, 3], "size": [8, 3, 5], "uv": [32, 0]}, {"origin": [-5, 16, -5], "size": [10, 5, 8], "uv": [0, 24]}, {"origin": [5, 15, -6], "size": [4, 4, 10], "uv": [60, 0]}, {"origin": [-9, 15, -6], "size": [4, 4, 10], "uv": [88, 0]}]}, {"name": "ala2", "parent": "body", "pivot": [-4, 21, 0], "cubes": [{"origin": [-4.25, 13, 0], "size": [1, 8, 6], "inflate": -0.25, "pivot": [-5, 21, 0], "rotation": [0, -15, 0], "uv": [0, 0]}, {"origin": [-4.25, 15, 5.45], "size": [1, 6, 6], "inflate": -0.3, "pivot": [-5, 21, 0], "rotation": [0, -15, 0], "uv": [22, 47]}]}, {"name": "ala1", "parent": "body", "pivot": [4, 21, 0], "cubes": [{"origin": [3.25, 13, 0], "size": [1, 8, 6], "inflate": -0.25, "pivot": [4, 21, 0], "rotation": [0, 10, 0], "uv": [40, 38]}, {"origin": [3.25, 15, 5.45], "size": [1, 6, 6], "inflate": -0.3, "pivot": [4, 21, 0], "rotation": [0, 10, 0], "uv": [48, 8]}]}, {"name": "head", "parent": "body", "pivot": [0, 19.3, -6.7]}, {"name": "cabeza2", "parent": "head", "pivot": [0, 19.3, -6.7], "cubes": [{"origin": [-1.5, 15.15, -9.725], "size": [3, 5, 3], "inflate": 0.2, "pivot": [0, 19.3, -7.7], "rotation": [42.5, 0, 0], "uv": [56, 27]}, {"origin": [-1.5, 19.3, -9.7], "size": [3, 5, 3], "inflate": 0.1, "pivot": [0, 19.3, -7.7], "rotation": [42.5, 0, 0], "uv": [26, 60]}]}, {"name": "montura0", "parent": "cabeza2", "pivot": [0, 19.3, -7.7], "cubes": [{"origin": [-2, 21.3, -10.7], "size": [4, 5, 8], "inflate": -0.2, "pivot": [0, 24.8, -5.7], "rotation": [17.5, 0, 0], "uv": [0, 111]}, {"origin": [-1.5, 22.3, -9.7], "size": [3, 1, 3], "inflate": 0.25, "pivot": [0, 19.3, -7.7], "rotation": [42.5, 0, 0], "uv": [53, 0]}]}, {"name": "cuello", "parent": "cabeza2", "pivot": [0, 22.3, -11.7], "cubes": [{"origin": [-1, 27, -11.4], "size": [2, 3, 2], "inflate": 0.05, "pivot": [0, 30, -11.5], "rotation": [-22.5, 0, 0], "uv": [0, 37]}, {"origin": [-1, 22, -12.5], "size": [2, 5, 2], "inflate": 0.15, "uv": [0, 24]}]}, {"name": "cabeza", "parent": "cuello", "pivot": [0, 29.2, -10.5], "cubes": [{"origin": [-1, 29.2, -11.5], "size": [2, 4, 2], "uv": [8, 0]}, {"origin": [-1, 32.2, -13], "size": [2, 2, 2], "inflate": -0.075, "pivot": [0, 33.2, -12], "rotation": [-7.5, 0, 0], "uv": [0, 42]}, {"origin": [-1, 32.6, -16.5], "size": [2, 2, 4], "inflate": -0.25, "pivot": [0, 32.6, -14.5], "rotation": [-15, 0, 0], "uv": [38, 59]}, {"origin": [-1.5, 33.6, -16.5], "size": [3, 2, 4], "inflate": -0.45, "pivot": [1, 33.6, -14.5], "rotation": [15, 0, 0], "uv": [56, 4]}, {"origin": [-1, 33.6, -17.5], "size": [2, 1, 4], "uv": [58, 16]}, {"origin": [-2, 33.2, -13.5], "size": [4, 4, 4], "uv": [32, 8]}]}, {"name": "root", "pivot": [0, 0, 0]}, {"name": "rotation", "parent": "root", "pivot": [0, 0, 0]}, {"name": "waist2", "parent": "rotation", "pivot": [0, 12, 0]}, {"name": "body_human", "parent": "waist2", "pivot": [0, 24, 0]}, {"name": "leftArm", "parent": "body_human", "pivot": [5, 22, 0]}, {"name": "la", "parent": "leftArm", "pivot": [6, 18, 0]}, {"name": "leftItem", "parent": "la", "pivot": [6, 15, 1]}, {"name": "rightArm", "parent": "body_human", "pivot": [-5, 22, 0]}, {"name": "ra", "parent": "rightArm", "pivot": [-6, 18, 0]}, {"name": "rightItem", "parent": "ra", "pivot": [-6, 17, 2]}]}]}