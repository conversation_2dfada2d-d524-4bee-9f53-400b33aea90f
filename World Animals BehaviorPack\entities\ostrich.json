{
  "format_version": "1.13.0",
  "minecraft:entity": {
    "description": {
      "identifier": "worldanimals:ostrich",
      "is_spawnable": true,
      "is_summonable": true,
      "is_experimental": false
    },

    "component_groups": {
      "minecraft:ostrich_0": {
		  "minecraft:variant": { "value": 0 }
	  },
      "minecraft:ostrich_1": {
		  "minecraft:variant": { "value": 1 }
	  },
      "minecraft:ostrich_2": {
		  "minecraft:variant": { "value": 2 }
	  },
      "minecraft:add_saddle": {
        "minecraft:interact": {
          "interactions": [
            {
              "play_sounds": "armor.equip_generic",
              "on_interact": {
                "filters": {
                  "all_of": [
                    { "test" :  "is_family", "subject" : "other", "value" :  "player"},
                    { "test" :  "has_equipment", "domain": "hand","subject" : "other", "value" :  "worldanimals:ostrich_saddle"}
                  ]
                },
                "event": "minecraft:on_saddle",
                "target": "self"
              },
              "use_item": true,
              "interact_text": "action.interact.attachchest"
            }
          ]
        }
      },
      "minecraft:add_armor": {
		"minecraft:mark_variant": { "value": 0 },
        "minecraft:interact": {
          "interactions": [
            {
              "on_interact": {
                "filters": { "test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:green_flag"},
                "event": "minecraft:green_flag"
              },
              "interact_text": "action.interact.unequip",
              "use_item": true,
              "play_sounds": "armor.equip_generic"
            },
            {
              "on_interact": {
                "filters": { "test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:red_flag"},
                "event": "minecraft:red_flag"
              },
              "interact_text": "action.interact.unequip",
              "use_item": true,
              "play_sounds": "armor.equip_generic"
            },
            {
              "on_interact": {
                "filters": { "test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:blue_flag"},
                "event": "minecraft:blue_flag"
              },
              "interact_text": "action.interact.unequip",
              "use_item": true,
              "play_sounds": "armor.equip_generic"
            },
            {
              "on_interact": {
                "filters": { "test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:orange_flag"},
                "event": "minecraft:orange_flag"
              },
              "interact_text": "action.interact.unequip",
              "use_item": true,
              "play_sounds": "armor.equip_generic"
            },
            {
              "on_interact": {
                "filters": { "test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:yellow_flag"},
                "event": "minecraft:yellow_flag"
              },
              "interact_text": "action.interact.unequip",
              "use_item": true,
              "play_sounds": "armor.equip_generic"
            }
          ]
        }
      },
      "minecraft:green_flag": {
        "minecraft:loot": {
          "table": "loot_tables/entities/ostrich/green_flag_loot.json"
        },
        "minecraft:interact": {
          "interactions": [
            {
              "on_interact": {
                "filters": { "test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:shears"},
                "event": "minecraft:removearmor"
              },
              "spawn_items": {
                "table": "loot_tables/entities/ostrich/green_flag.json"
              },
              "interact_text": "action.interact.unequip",
              "use_item": false,
              "hurt_item": 2,
              "play_sounds": "armor.equip_generic"
            }
          ]
        },
        "minecraft:mark_variant": {
          "value": 1
        }
      },
      "minecraft:red_flag": {
        "minecraft:loot": {
          "table": "loot_tables/entities/ostrich/red_flag_loot.json"
        },
        "minecraft:interact": {
          "interactions": [
            {
              "on_interact": {
                "filters": { "test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:shears"},
                "event": "minecraft:removearmor"
              },
              "spawn_items": {
                "table": "loot_tables/entities/ostrich/red_flag.json"
              },
              "interact_text": "action.interact.unequip",
              "use_item": false,
              "hurt_item": 2,
              "play_sounds": "armor.equip_generic"
            }
          ]
        },
        "minecraft:mark_variant": {
          "value": 2
        }
      },
      "minecraft:blue_flag": {
        "minecraft:loot": {
          "table": "loot_tables/entities/ostrich/blue_flag_loot.json"
        },
        "minecraft:interact": {
          "interactions": [
            {
              "on_interact": {
                "filters": { "test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:shears"},
                "event": "minecraft:removearmor"
              },
              "spawn_items": {
                "table": "loot_tables/entities/ostrich/blue_flag.json"
              },
              "interact_text": "action.interact.unequip",
              "use_item": false,
              "hurt_item": 2,
              "play_sounds": "armor.equip_generic"
            }
          ]
        },
        "minecraft:mark_variant": {
          "value": 3
        }
      },
      "minecraft:yellow_flag": {
        "minecraft:loot": {
          "table": "loot_tables/entities/ostrich/yellow_flag.json"
        },
        "minecraft:interact": {
          "interactions": [
            {
              "on_interact": {
                "filters": { "test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:shears"},
                "event": "minecraft:removearmor"
              },
              "spawn_items": {
                "table": "loot_tables/entities/ostrich/yellow_flag.json"
              },
              "interact_text": "action.interact.unequip",
              "use_item": false,
              "hurt_item": 2,
              "play_sounds": "armor.equip_generic"
            }
          ]
        },
        "minecraft:mark_variant": {
          "value": 4
        }
      },
      "minecraft:orange_flag": {
        "minecraft:loot": {
          "table": "loot_tables/entities/ostrich/orange_flag_loot.json"
        },
        "minecraft:interact": {
          "interactions": [
            {
              "on_interact": {
                "filters": { "test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:shears"},
                "event": "minecraft:removearmor"
              },
              "spawn_items": {
                "table": "loot_tables/entities/ostrich/orange_flag.json"
              },
              "interact_text": "action.interact.unequip",
              "use_item": false,
              "hurt_item": 2,
              "play_sounds": "armor.equip_generic"
            }
          ]
        },
        "minecraft:mark_variant": {
          "value": 5
        }
      },
      "minecraft:ostrich_baby": {
        "minecraft:tameable": {
          "probability": 1.0,
          "tame_items": "worldanimals:collar",
          "tame_event": {
            "event": "minecraft:on_tame",
            "target": "self"
          }
        },
        "minecraft:is_baby": {
        },
        "minecraft:scale": {
          "value": 0.4
        },

        "minecraft:ageable": {
          "duration": 1200,
          "feed_items": [
            {
              "item": "minecraft:apple",
              "growth": 0.5
            },
            {
              "item": "minecraft:golden_apple",
              "growth": 0.5
            },
            {
              "item": "minecraft:carrot",
              "growth": 0.5
            },
            {
              "item": "minecraft:wheat",
              "growth": 0.5
            }
          ],
          "grow_up": {
            "event": "minecraft:ageable_grow_up",
            "target": "self"
          }
        },

        "minecraft:behavior.follow_parent": {
          "priority": 5,
          "speed_multiplier": 1.0
        }
      },

      "minecraft:ostrich_adult": {
        "minecraft:spawn_entity": {
          "min_wait_time": 300,
          "max_wait_time": 600,
          "spawn_sound": "plop",
          "spawn_item": "worldanimals:ostrich_egg_spawn_egg",
          "filters": {
            "test": "rider_count", "subject": "self", "operator": "==", "value": 0
          }
        },
        "minecraft:tameable": {
          "probability": 1.0,
          "tame_items": "worldanimals:collar",
          "tame_event": {
            "event": "minecraft:on_tame",
            "target": "self"
          }
        },
        "minecraft:scale": {
          "value": 1.25
        },
        "minecraft:behavior.nearest_attackable_target": {
          "priority": 4,
          "attack_interval": 10,
          "reselect_targets": true,
          "entity_types": [
            {
              "filters": {
                "any_of": [
                  { "test" :  "is_family", "subject" : "other", "value" :  "skeleton"},
                  { "test" :  "is_family", "subject" : "other", "value" :  "zombie"}
                ] 
              },
              "max_dist": 16
            },
            {
              "filters": {
                "all_of": [
                  {
                    "test": "is_family",
                    "subject": "other",
                    "value": "baby_turtle"
                  },
                  {
                    "test": "in_water",
                    "subject": "other",
                    "operator": "!=",
                    "value": true
                  }
                ]
              },
              "max_dist": 16
            },
            {
              "filters": {
                "all_of": [
                  {
                    "test": "is_family",
                    "subject": "other",
                    "value": "skeleton"
                  },
                  {
                    "test": "is_underwater",
                    "subject": "other",
                    "operator": "!=",
                    "value": true
                  }
                ]
              },
              "max_dist": 16
            }
          ],
          "must_see": true
        },
        "minecraft:experience_reward": {
          "on_bred": "Math.Random(1,7)",
          "on_death": "query.last_hit_by_player ? Math.Random(1,3) : 0"
        },
        "minecraft:collision_box": {
          "width": 0.9,
          "height": 1.87
        },
        "minecraft:behavior.breed": {
          "priority": 4,
          "speed_multiplier": 1.0
        },
        "minecraft:breedable": {
          "require_tame": true,
          "inherit_tamed": false,
          "breeds_with": {
            "mate_type": "worldanimals:ostrich",
            "baby_type": "worldanimals:ostrich",
            "breed_event": {
              "event": "minecraft:entity_born",
              "target": "baby"
            }
          },
          "breed_items": [ "hay_block" ]
        }
      },

      "minecraft:ostrich_wandering_trader": {
        "minecraft:on_friendly_anger": {
          "event": "minecraft:defend_wandering_trader",
          "target": "self"
        }, 
        "minecraft:environment_sensor": {
          // If this is a Wandering Trader's ostrich and it was just released from its leash, make it tame
          "triggers": {
            "filters": {
              "all_of": [
                { "test": "is_leashed", "subject": "self", "value": false },
                { "test": "has_component", "subject": "self", "operator": "!=", "value" :  "minecraft:is_tamed" }
              ]
            },
            "event": "minecraft:on_tame"
          }
        }
      },

      "minecraft:strength_1": {
        "minecraft:strength": {
          "value": 1,
          "max": 5
        }
      },
      "minecraft:strength_2": {
        "minecraft:strength": {
          "value": 2,
          "max": 5
        }
      },
      "minecraft:strength_3": {
        "minecraft:strength": {
          "value": 3,
          "max": 5
        }
      },
      "minecraft:strength_4": {
        "minecraft:strength": {
          "value": 4,
          "max": 5
        }
      },
      "minecraft:strength_5": {
        "minecraft:strength": {
          "value": 5,
          "max": 5
        }
      },

      "minecraft:ostrich_wild": {
	  
      },


      "minecraft:ostrich_tamed": {
        "minecraft:behavior.owner_hurt_by_target": {
          "priority": 1
        },
        "minecraft:interact": {
          "interactions": [
            {
              "play_sounds": "armor.equip_generic",
              "on_interact": {
                "filters": {
                  "all_of": [
                    { "test" :  "is_family", "subject" : "other", "value" :  "player"},
                    { "test" :  "has_equipment", "domain": "hand","subject" : "other", "value" :  "worldanimals:elephant_saddle"}
                  ]
                },
                "event": "minecraft:on_saddle",
                "target": "self"
              },
              "use_item": true,
              "interact_text": "action.interact.attachchest"
            }
          ]
        },
        "minecraft:behavior.owner_hurt_target": {
          "priority": 2
        },
        "minecraft:is_tamed": {
        },
        "minecraft:sittable": {
        },
      "minecraft:behavior.stay_while_sitting": {
        "priority": 3
      },
        "minecraft:behavior.follow_owner": {
          "priority": 6,
          "speed_multiplier": 1.0,
          "start_distance": 12,
          "stop_distance": 3
        },
        "minecraft:rideable": {
          "priority": 0,
          "seat_count": 1,
          "crouching_skip_interact": true,
          "family_types": [
            "player"
          ],
          "interact_text": "action.interact.ride.horse",
        "seats": [
          {
         "position": [ 0.0, 1.15, -0.15 ],
         "min_rider_count": 0,
         "max_rider_count": 7
          }
        ]

        }
      },

      "minecraft:ostrich_saddle": {
	          "minecraft:behavior.player_ride_tamed": {
        },
	  "minecraft:inventory": {
        "inventory_size": 54,
        "container_type": "container", 
        "private": false
      },
        "minecraft:input_ground_controlled": {
        },
        "minecraft:can_power_jump": {
        },
        "minecraft:is_saddled": {

        },
        "minecraft:loot": {
          "table": "loot_tables/entities/ostrich/saddle.json"
        }
      },

      "minecraft:ostrich_angry": {
        "minecraft:angry": {
          "duration": 4,
          "broadcast_anger": false,
          "calm_event": {
            "event": "minecraft:on_calm",
            "target": "self"
          }
        }
      },
      "minecraft:ostrich_angry_wolf": {
        "minecraft:angry": {
          "duration": 25,
          "broadcast_anger": true,
          "broadcast_range": 20,
          "calm_event": {
            "event": "minecraft:on_calm",
            "target": "self"
          }
        },
        "minecraft:on_target_acquired": {
        }
      },
      "minecraft:ostrich_defend_trader": {
        "minecraft:angry": {
          "duration": 10,
          "calm_event": {
            "event": "minecraft:on_calm",
            "target": "self"
          }
        },
        "minecraft:behavior.ranged_attack": {
          "priority": 2,
          "attack_radius": 64,
          "charge_shoot_trigger": 2,
          "charge_charged_trigger": 1
        }
      },

      "minecraft:in_caravan": {
        "minecraft:damage_sensor": {
          "triggers": {
            "cause": "all",
            "deals_damage": true
          }
        }
      }

    },


    "components": {
      "minecraft:type_family": {
        "family": [ "ostrich", "animal" ]
      },
      "minecraft:breathable": {
        "total_supply": 15,
        "suffocate_time": 0
      },
        "minecraft:loot": {
          "table": "loot_tables/entities/ostrich/ostrich.json"
        },
      "minecraft:nameable": {
      },
      "minecraft:health": {
        "value": {
          "range_min": 50,
          "range_max": 50
        }
      },
        "minecraft:interact": {
          "interactions": [
				{
					"on_interact": {
						"event": "become_cow",
						"target": "self",
						"filters": {
							"all_of": [
								{
									"test": "has_equipment",
									"domain": "hand",
									"subject": "other",
									"value": "worldanimals:mammoth_dna"
								}
							]
						}
					},
					"use_item": true,
					"interact_text": "UPGRADE"
				},
            {
              "on_interact": {
                "filters": {
                  "all_of": [
                    { "test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:syringe"},
                    { "test" :  "is_family", "subject" : "other", "value" :  "player"}
                  ]
                }
              },
              "use_item": true,
              "transform_to_item": "worldanimals:elephant_dna",
              "play_sounds": "milk",
              "interact_text": "action.interact.milk"
            }
          ]
        },
      "minecraft:attack": {
        "damage": 7
      },
        "minecraft:behavior.nearest_attackable_target": {
          "priority": 5,
          "attack_interval": 10,
          "entity_types": [
            {
              "filters": { "test" :  "is_family", "subject" : "other", "value" :  "skeleton"},
              "max_dist": 16
            }
          ],
          "must_see": true
        },
      "minecraft:behavior.melee_attack": {
        "priority": 5,
        "target_dist": 1.2,
        "track_target": true,
        "reach_multiplier": 1.0
      },
      "minecraft:hurt_on_condition": {
        "damage_conditions": [
          {
            "filters": { "test": "in_lava", "subject": "self", "operator": "==", "value": true },
            "cause": "lava",
            "damage_per_tick": 4
          }
        ]
      },
      "minecraft:movement": {
        "value": 0.2
      },
      "minecraft:navigation.walk": {
        "can_path_over_water": true,
        "avoid_damage_blocks": true
      },
      "minecraft:movement.basic": {
      },
      "minecraft:jump.static": {
      },
      "minecraft:follow_range": {
        "value": 40,
        "max": 40
      },
      "minecraft:leashable": {
        "soft_distance": 4.0,
        "hard_distance": 6.0,
        "max_distance": 10.0,
        "can_be_stolen": true
      },
      "minecraft:balloonable": {
      },
      "minecraft:healable": {
        "items": [
          {
            "item": "minecraft:apple",
            "heal_amount": 5
          },
          {
            "item": "minecraft:golden_apple",
            "heal_amount": 24
          },
          {
            "item": "minecraft:carrot",
            "heal_amount": 3
          },
          {
            "item": "minecraft:wheat",
            "heal_amount": 3
          }
        ]
      },

      "minecraft:behavior.float": {
        "priority": 0
      },
      "minecraft:celebrate": {
        "minecraft:behavior.celebrate": {
          "priority": 5,
          "celebration_sound": "celebrate",
          "sound_interval": {
            "range_min": 2.0,
            "range_max": 7.0
          },
          "jump_interval": {
            "range_min": 1.0,
            "range_max": 3.5
          },
          "duration": 30.0,
          "on_celebration_end_event": {
            "event": "minecraft:stop_celebrating",
            "target": "self"
          }
        }
      },
        "minecraft:celebrate_hunt": {
          "celebration_targets": {
            "all_of": [
              {
                "test": "is_family",
                "value": "monster"
              }
            ]
          },
          "broadcast": true,
          "duration": 10,
          "celebrate_sound": "celebrate",
          "sound_interval": {
            "range_min": 2.0,
            "range_max": 5.0
          },
          "radius": 16
        },
      "minecraft:behavior.run_around_like_crazy": {
        "priority": 1,
        "speed_multiplier": 1.2
      },
      "minecraft:behavior.random_stroll": {
        "priority": 6,
        "speed_multiplier": 0.7
      },
      "minecraft:behavior.look_at_player": {
        "priority": 7,
        "look_distance": 6.0,
        "probability": 0.02
      },
      "minecraft:behavior.random_look_around": {
        "priority": 8
      },
        "minecraft:tameable": {
          "probability": 1.0,
          "tame_items": "worldanimals:collar",
          "tame_event": {
            "event": "minecraft:on_tame",
            "target": "self"
          }
        },
      "minecraft:behavior.mount_pathing": {
        "priority": 1,
        "speed_multiplier": 1.25,
        "target_dist": 0.0,
        "track_target": true
      },
      "minecraft:behavior.hurt_by_target": {
        "priority": 1,
        "hurt_owner": true
      },
      "minecraft:damage_sensor": {
        "triggers": {
          "cause": "all",
          "deals_damage": true,
          "on_damage": {
            "filters": { "test": "in_caravan", "value": false },
            "event": "minecraft:become_angry"
          }
        }
      },
      "minecraft:behavior.nearest_attackable_target": {
        "priority": 2,
        "attack_interval": 16,
        "entity_types": [
          {
            "filters": {
                "all_of": [
                  { "test" :  "is_family", "subject" : "other", "value" :  "wolf"},
                  { "test" :  "has_component","subject" : "other", "operator": "!=", "value" :  "minecraft:is_tamed"}
                ]
              },
            "max_dist": 10
          }
        ],
        "must_see": false,
        "must_reach": true
      },
      "minecraft:on_target_acquired": {
        "filters": {
                "all_of": [
                  { "test" :  "is_family", "subject" : "target", "value" :  "wolf"},
                  { "test" :  "has_component","subject" : "target", "operator": "!=", "value" :  "minecraft:is_tamed"}
                ]
              },
        "event": "minecraft:mad_at_wolf",
        "target": "self"
      },
      "minecraft:on_target_escape": {
        "filters": {
                "all_of": [
                  { "test" :  "is_family", "subject" : "target", "value" :  "wolf"},
                  { "test" :  "has_component","subject" : "target", "operator": "!=", "value" :  "minecraft:is_tamed"}
                ]
              },
        "event": "minecraft:on_calm",
        "target": "self"
      },
	    "minecraft:physics": {
      },
      "minecraft:pushable": {
        "is_pushable": true,
        "is_pushable_by_piston": true
      }
    },


    "events": {
      "become_cow": {
        "remove": {
        },
        "add": {
          "component_groups": [
            "minecraft:mooberry_become_cow"
          ]
        },
         "run_command": {
              "command": [
                "summon worldanimals:mammoth ~ ~ ~ minecraft:entity_spawned"
             ]
          }
      },
      "minecraft:entity_spawned": {
        "sequence": [
          {
            "randomize": [
              {
                "weight": 90,
                "remove": {
                },
                "add": {
                  "component_groups": [
                    "minecraft:ostrich_adult",
                    "minecraft:ostrich_wild"
                  ]
                }
              },
              {
                "weight": 10,
                "remove": {
                },
                "add": {
                  "component_groups": [
                    "minecraft:ostrich_baby"
                  ]

                }
              }
            ]
          },
          {
            "randomize": [
              {
                "weight": 33,
                "add": {
                  "component_groups": [
                    "minecraft:ostrich_0"
                  ]
                }
              },
              {
                "weight": 33,
                "add": {
                  "component_groups": [
                    "minecraft:ostrich_1"
                  ]
                }
              },
              {
                "weight": 33,
                "add": {
                  "component_groups": [
                    "minecraft:ostrich_2"
                  ]
                }
              }
            ]
          }
        ]
      },
      "minecraft:entity_transformed": {
        "add": {
          "component_groups": [
            "minecraft:ostrich_baby",
                "minecraft:ostrich_wild"
          ]
        },
        "sequence": [
          {
            "randomize": [
              {
                "weight": 33,
                "add": {
                  "component_groups": [
                    "minecraft:ostrich_0"
                  ]
                }
              },
              {
                "weight": 33,
                "add": {
                  "component_groups": [
                    "minecraft:ostrich_1"
                  ]
                }
              },
              {
                "weight": 33,
                "add": {
                  "component_groups": [
                    "minecraft:ostrich_2"
                  ]
                }
              }
            ]
          }
        ]
      },
      "minecraft:entity_born": {
        "add": {
          "component_groups": [
            "minecraft:ostrich_baby"
          ]
        }
      },

      "minecraft:from_wandering_trader": {
        "sequence": [
          {
            "add": {
              "component_groups": [
                "minecraft:ostrich_adult",
                "minecraft:ostrich_wandering_trader"
              ]
            }
          },
          {
            "randomize": [
              {
                "weight": 32,
                "add": {
                  "component_groups": [
                    "minecraft:strength_1"
                  ]
                }
              },
              {
                "weight": 32,
                "add": {
                  "component_groups": [
                    "minecraft:strength_2"
                  ]
                }
              },
              {
                "weight": 32,
                "add": {
                  "component_groups": [
                    "minecraft:strength_3"
                  ]
                }
              },
              {
                "weight": 2,
                "add": {
                  "component_groups": [
                    "minecraft:strength_4"
                  ]
                }
              },
              {
                "weight": 2,
                "add": {
                  "component_groups": [
                    "minecraft:strength_5"
                  ]
                }
              }
            ]
          },
          {
            "randomize": [
              {
                "weight": 25,
                "add": {
                  "component_groups": [
                    "minecraft:ostrich_creamy"
                  ]
                }
              },
              {
                "weight": 25,
                "add": {
                  "component_groups": [
                    "minecraft:ostrich_white"
                  ]
                }
              },
              {
                "weight": 25,
                "add": {
                  "component_groups": [
                    "minecraft:ostrich_brown"
                  ]
                }
              },
              {
                "weight": 25,
                "add": {
                  "component_groups": [
                    "minecraft:ostrich_gray"
                  ]
                }
              }
            ]
          }
        ]
      },

      "minecraft:ageable_grow_up": {
        "remove": {
          "component_groups": [
            "minecraft:ostrich_baby"
          ]
        },
        "add": {
          "component_groups": [
            "minecraft:ostrich_adult",
            "minecraft:ostrich_wild"
          ]
        }
      },

      "minecraft:on_tame": {
        "remove": {
          "component_groups": [
            "minecraft:ostrich_wild"
          ]
        },
        "add": {
          "component_groups": [
            "minecraft:ostrich_tamed",
			"minecraft:add_saddle"
          ]
        }
      },
      "minecraft:join_caravan": {
        "add": {
          "component_groups": [
            "minecraft:in_caravan"
          ]
        }
      },
      "minecraft:leave_caravan": {
        "remove": {
          "component_groups": [
            "minecraft:in_caravan"
          ]
        }
      },
      "minecraft:mad_at_wolf": {
        "add": {
          "component_groups": [
            "minecraft:ostrich_angry_wolf"
          ]
        }
      },
      "minecraft:defend_wandering_trader": {
        "add": {
          "component_groups": [
            "minecraft:ostrich_defend_trader"
          ]
        }
      },
      "minecraft:become_angry": {
        "add": {
          "component_groups": [
            "minecraft:ostrich_angry"
          ]
        }
      },
      "minecraft:on_calm": {
        "remove": {
          "component_groups": [
            "minecraft:ostrich_angry",
            "minecraft:ostrich_angry_wolf",
            "minecraft:ostrich_defend_trader"
          ]
        }
      },

      "minecraft:on_saddle": {
        "add": {
          "component_groups": [
            "minecraft:ostrich_saddle",
			"minecraft:add_armor"
          ]
        }
      },

      "minecraft:off_saddle": {
        "remove": {
          "component_groups": [
            "minecraft:ostrich_saddle"
          ]
        }
      },
      "minecraft:removearmor": {
        "add": {
          "component_groups": [
            "minecraft:add_armor"
          ]
        },
        "remove": {
          "component_groups": [
            "minecraft:yellow_flag",
			"minecraft:blue_flag",
			"minecraft:red_flag",
			"minecraft:green_flag",
			"minecraft:orange_flag"
          ]
        }
      },
      "minecraft:green_flag": {
        "remove": {
          "component_groups": [
            "minecraft:add_armor"
          ]
        },
        "add": {
          "component_groups": [
            "minecraft:green_flag"
          ]
        }
      },
      "minecraft:blue_flag": {
        "remove": {
          "component_groups": [
            "minecraft:add_armor"
          ]
        },
        "add": {
          "component_groups": [
            "minecraft:blue_flag"
          ]
        }
      },
      "minecraft:yellow_flag": {
        "remove": {
          "component_groups": [
            "minecraft:add_armor"
          ]
        },
        "add": {
          "component_groups": [
            "minecraft:yellow_flag"
          ]
        }
      },
      "minecraft:red_flag": {
        "remove": {
          "component_groups": [
            "minecraft:add_armor"
          ]
        },
        "add": {
          "component_groups": [
            "minecraft:red_flag"
          ]
        }
      },
      "minecraft:orange_flag": {
        "remove": {
          "component_groups": [
            "minecraft:add_armor"
          ]
        },
        "add": {
          "component_groups": [
            "minecraft:orange_flag"
          ]
        }
      }
    }
  }
}