{"format_version": "1.8.0", "minecraft:client_entity": {"description": {"identifier": "worldanimals:ant", "materials": {"default": "parrot"}, "textures": {"default": "textures/entity/ant/ant2", "red": "textures/entity/ant/ant"}, "geometry": {"default": "geometry.ant"}, "scripts": {"pre_animation": ["variable.state = query.is_dancing ? 3 : (query.is_sitting ? 2 : (!query.is_on_ground && !query.is_jumping && !query.is_riding ? 0 : 1));", "variable.dance.x = Math.cos(query.life_time * 57.3 * 20.0);", "variable.dance.y = -Math.sin(query.life_time * 57.3 * 20.0);", "variable.wing_flap = ((math.sin(query.wing_flap_position * 57.3) + 1) * query.wing_flap_speed);"]}, "animations": {"setup": "animation.ant.idle", "walk": "animation.ant.walk", "look_at_target": "animation.common.look_at_target", "baby_transform": "animation.llama.baby_transform", "wolf_sitting": "animation.ant.sit"}, "animation_controllers": [{"wolf_sitting": "controller.animation.wolf.sitting"}, {"move": "controller.animation.llama.move"}, {"baby": "controller.animation.llama.baby"}], "render_controllers": ["controller.render.ant"], "spawn_egg": {"texture": "egg_ant", "texture_index": 0}}}}