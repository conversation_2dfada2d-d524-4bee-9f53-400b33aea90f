{"format_version": "1.16.0", "minecraft:entity": {"description": {"identifier": "worldanimals:butterfly", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"minecraft:parrot_red": {"minecraft:variant": {"value": 0}, "minecraft:loot": {"table": "loot_tables/entities/butterfly/butterfly_elytra2.json"}}, "minecraft:parrot_blue": {"minecraft:variant": {"value": 1}, "minecraft:loot": {"table": "loot_tables/entities/butterfly/butterfly_elytra0.json"}}, "minecraft:parrot_green": {"minecraft:variant": {"value": 2}, "minecraft:loot": {"table": "loot_tables/entities/butterfly/butterfly_elytra1.json"}}, "minecraft:parrot_cyan": {"minecraft:variant": {"value": 3}, "minecraft:loot": {"table": "loot_tables/entities/butterfly/butterfly_elytra3.json"}}, "minecraft:parrot_silver": {"minecraft:variant": {"value": 4}, "minecraft:loot": {"table": "loot_tables/entities/butterfly/butterfly_elytra4.json"}}}, "components": {"minecraft:type_family": {"family": ["butterfly", "mob"]}, "minecraft:breathable": {"total_supply": 15, "suffocate_time": 0}, "minecraft:collision_box": {"width": 0.5, "height": 0.9}, "minecraft:damage_sensor": {"triggers": {"cause": "fall", "deals_damage": false}}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:movement": {"value": 0.1}, "minecraft:navigation.float": {"can_path_over_water": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.float_wander": {"xz_dist": 10, "y_dist": 7, "y_offset": -2.0, "random_reselect": true, "float_duration": [0.1, 0.35]}, "minecraft:can_fly": {}, "minecraft:health": {"value": 6, "max": 6}, "minecraft:nameable": {}, "minecraft:physics": {}}, "events": {"minecraft:entity_spawned": {"randomize": [{"weight": 20, "add": {"component_groups": ["minecraft:parrot_red"]}}, {"weight": 20, "add": {"component_groups": ["minecraft:parrot_blue"]}}, {"weight": 20, "add": {"component_groups": ["minecraft:parrot_green"]}}, {"weight": 20, "add": {"component_groups": ["minecraft:parrot_cyan"]}}, {"weight": 20, "add": {"component_groups": ["minecraft:parrot_silver"]}}]}}}}