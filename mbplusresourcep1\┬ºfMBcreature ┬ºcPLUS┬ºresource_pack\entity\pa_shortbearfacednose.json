{"format_version": "1.8.0", "minecraft:client_entity": {"description": {"identifier": "pa:shortbear<PERSON><PERSON><PERSON>", "min_engine_version": "1.8.0", "materials": {"default": "s<PERSON><PERSON>"}, "textures": {"undyed": "textures/entity/pamobile/pa_shortbearfacednose", "white": "textures/entity/pamobile/pa_shortbearfacednose", "orange": "textures/entity/pamobile/pa_shortbearfacednose", "magenta": "textures/entity/pamobile/pa_shortbearfacednose", "light_blue": "textures/entity/pamobile/pa_shortbearfacednose", "yellow": "textures/entity/pamobile/pa_shortbearfacednose", "lime": "textures/entity/pamobile/pa_shortbearfacednose", "pink": "textures/entity/pamobile/pa_shortbearfacednose", "gray": "textures/entity/pamobile/pa_shortbearfacednose", "silver": "textures/entity/pamobile/pa_shortbearfacednose", "cyan": "textures/entity/pamobile/pa_shortbearfacednose", "purple": "textures/entity/pamobile/pa_shortbearfacednose", "blue": "textures/entity/pamobile/pa_shortbearfacednose", "brown": "textures/entity/pamobile/pa_shortbearfacednose", "green": "textures/entity/pamobile/pa_shortbearfacednose", "red": "textures/entity/pamobile/pa_shortbearfacednose", "black": "textures/entity/pamobile/pa_shortbearfacednose"}, "geometry": {"default": "geometry.pa_shortbearfacednose"}, "spawn_egg": {"texture": "pa:shortbear<PERSON><PERSON><PERSON>", "texture_index": 0}, "scripts": {"pre_animation": ["variable.Shulker.LidPositionFactor = 180 * (0.5 + variable.Shulker.PeekAmount);", "variable.Shulker.LidRotationFactor = -1 + Math.sin(180 * (0.5 + variable.Shulker.PeekAmount));", "variable.Shulker.UpFacing = variable.Shulker.FacingDirection == 1;", "variable.Shulker.NorthFacing = variable.Shulker.FacingDirection == 2;", "variable.Shulker.SouthFacing = variable.Shulker.FacingDirection == 3;", "variable.Shulker.WestFacing = variable.Shulker.FacingDirection == 4;", "variable.Shulker.EastFacing = variable.Shulker.FacingDirection == 5;", "variable.Shulker.XPreRotation = variable.Shulker.UpFacing * 180 + variable.Shulker.NorthFacing * 90 - variable.Shulker.SouthFacing * 90;", "variable.Shulker.ZPreRotation = variable.Shulker.NorthFacing * 180 + variable.Shulker.WestFacing * 90 - variable.Shulker.EastFacing * 90;", "variable.Shulker.XOffset = -variable.Shulker.WestFacing * 7.99 + variable.Shulker.EastFacing * 7.99;", "variable.Shulker.YOffset = variable.Shulker.UpFacing * 16 + variable.Shulker.NorthFacing * 7.99 + variable.Shulker.SouthFacing * 7.99 + variable.Shulker.WestFacing * 7.99 + variable.Shulker.EastFacing * 7.99;", "variable.Shulker.ZOffset = variable.Shulker.NorthFacing * 7.99 - variable.Shulker.SouthFacing * 7.99;"]}, "animations": {"facing": "animation.shulker.facing", "move": "animation.shulker.move", "look_at_target": "animation.common.look_at_target"}, "animation_controllers": [{"facing": "controller.animation.shulker.facing"}, {"move": "controller.animation.shulker.move"}], "render_controllers": ["controller.render.shulker"]}}}