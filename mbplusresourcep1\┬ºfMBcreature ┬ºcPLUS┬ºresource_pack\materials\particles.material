{"materials": {"version": "1.0.0", "particles_base": {"vertexShader": "shaders/color_uv.vertex", "vrGeometryShader": "shaders/color_uv.geometry", "fragmentShader": "shaders/color_texture.fragment", "vertexFields": [{"field": "Position"}, {"field": "Color"}, {"field": "UV0"}], "+samplerStates": [{"samplerIndex": 0, "textureFilter": "Point"}], "msaaSupport": "Both"}, "particles_opaque:particles_base": {"+defines": ["ENABLE_FOG"], "+states": ["DisableAlphaWrite"]}, "particles_glow_glass:particles_base": {"+defines": ["USE_ONLY_EMISSIVE"], "+states": ["Blending", "DisableCulling"], "blendSrc": "One", "blendDst": "One"}, "particles_charged:particles_base": {"+defines": ["ENABLE_FOG"], "+states": ["Blending", "DisableAlphaWrite"], "blendSrc": "One", "blendDst": "One"}, "particles_glass:particles_base": {"+defines": ["ENABLE_FOG"], "+states": ["Blending"]}, "particles_alpha:particles_base": {"+defines": ["ALPHA_TEST", "ENABLE_FOG"], "+states": ["DisableAlphaWrite"]}, "particles_blend:particles_base": {"+defines": ["ENABLE_FOG"], "+states": ["Blending", "DisableCulling", "DisableDepthWrite"]}, "particles_effects:particles_alpha": {"+defines": ["EFFECTS_OFFSET"], "msaaSupport": "Both"}, "particles_random_test": {"vertexShader": "shaders/particle_random_test.vertex", "vrGeometryShader": "shaders/color_uv.geometry", "fragmentShader": "shaders/color_texture.fragment", "vertexFields": [{"field": "Position"}, {"field": "Color"}, {"field": "Normal"}, {"field": "UV0"}], "+samplerStates": [{"samplerIndex": 0, "textureFilter": "Point"}], "+defines": ["ALPHA_TEST", "ENABLE_FOG"], "+states": ["DisableAlphaWrite"], "msaaSupport": "Both"}}}