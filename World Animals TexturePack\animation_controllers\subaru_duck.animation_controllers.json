{"format_version": "1.10.0", "animation_controllers": {"controller.animation.subaru_duck.general": {"initial_state": "default", "states": {"casting": {"animations": ["look_at_target", "casting"], "particle_effects": [{"effect": "spell", "locator": "left_hand"}, {"effect": "spell", "locator": "right_hand"}], "transitions": [{"default": "!query.is_casting"}]}, "celebrating": {"animations": ["celebrating"], "blend_transition": 0.2, "blend_via_shortest_path": true, "transitions": [{"default": "!query.is_celebrating"}]}, "default": {"animations": ["look_at_target", "general"], "blend_transition": 0.2, "blend_via_shortest_path": true, "transitions": [{"casting": "query.is_casting"}, {"celebrating": "query.is_celebrating"}]}}}, "controller.animation.subaru_duck.riding": {"initial_state": "default", "states": {"default": {"transitions": [{"riding": "query.is_riding"}]}, "riding": {"animations": ["girlfriend_riding"], "transitions": [{"default": "!query.is_riding"}]}}}, "controller.animation.subaru_duck.general.v1.0": {"initial_state": "default", "states": {"casting": {"animations": ["look_at_target", "casting"], "particle_effects": [{"effect": "spell", "locator": "left_hand"}, {"effect": "spell", "locator": "right_hand"}], "transitions": [{"default": "!query.is_casting"}]}, "default": {"animations": ["look_at_target", "general"], "transitions": [{"casting": "query.is_casting"}]}}}}}