{"format_version": "1.13.0", "minecraft:entity": {"description": {"identifier": "worldanimals:caracal", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"minecraft:interacciones": {"minecraft:interact": {"interactions": [{"on_interact": {"filters": {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:black_scarf"}, "event": "minecraft:black_scarf_on"}, "interact_text": "action.interact.armadura", "use_item": true, "play_sounds": "armor.equip_leather"}, {"on_interact": {"filters": {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:blue_scarf"}, "event": "minecraft:blue_scarf_on"}, "interact_text": "action.interact.armadura", "use_item": true, "play_sounds": "armor.equip_leather"}, {"on_interact": {"filters": {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:brown_scarf"}, "event": "minecraft:brown_scarf_on"}, "interact_text": "action.interact.armadura", "use_item": true, "play_sounds": "armor.equip_leather"}, {"on_interact": {"filters": {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:cian_scarf"}, "event": "minecraft:cian_scarf_on"}, "interact_text": "action.interact.armadura", "use_item": true, "play_sounds": "armor.equip_leather"}, {"on_interact": {"filters": {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:gray_scarf"}, "event": "minecraft:gray_scarf_on"}, "interact_text": "action.interact.armadura", "use_item": true, "play_sounds": "armor.equip_leather"}, {"on_interact": {"filters": {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:green_scarf"}, "event": "minecraft:green_scarf_on"}, "interact_text": "action.interact.armadura", "use_item": true, "play_sounds": "armor.equip_leather"}, {"on_interact": {"filters": {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:light_blue_scarf"}, "event": "minecraft:light_blue_scarf_on"}, "interact_text": "action.interact.armadura", "use_item": true, "play_sounds": "armor.equip_leather"}, {"on_interact": {"filters": {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:light_gray_scarf"}, "event": "minecraft:light_gray_scarf_on"}, "interact_text": "action.interact.armadura", "use_item": true, "play_sounds": "armor.equip_leather"}, {"on_interact": {"filters": {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:lime_scarf"}, "event": "minecraft:lime_scarf_on"}, "interact_text": "action.interact.armadura", "use_item": true, "play_sounds": "armor.equip_leather"}, {"on_interact": {"filters": {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:magenta_scarf"}, "event": "minecraft:magenta_scarf_on"}, "interact_text": "action.interact.armadura", "use_item": true, "play_sounds": "armor.equip_leather"}, {"on_interact": {"filters": {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:orange_scarf"}, "event": "minecraft:orange_scarf_on"}, "interact_text": "action.interact.armadura", "use_item": true, "play_sounds": "armor.equip_leather"}, {"on_interact": {"filters": {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:pink_scarf"}, "event": "minecraft:pink_scarf_on"}, "interact_text": "action.interact.armadura", "use_item": true, "play_sounds": "armor.equip_leather"}, {"on_interact": {"filters": {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:purple_scarf"}, "event": "minecraft:purple_scarf_on"}, "interact_text": "action.interact.armadura", "use_item": true, "play_sounds": "armor.equip_leather"}, {"on_interact": {"filters": {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:red_scarf"}, "event": "minecraft:red_scarf_on"}, "interact_text": "action.interact.armadura", "use_item": true, "play_sounds": "armor.equip_leather"}, {"on_interact": {"filters": {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:white_scarf"}, "event": "minecraft:white_scarf_on"}, "interact_text": "action.interact.armadura", "use_item": true, "play_sounds": "armor.equip_leather"}, {"on_interact": {"filters": {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "worldanimals:yellow_scarf"}, "event": "minecraft:yellow_scarf_on"}, "interact_text": "action.interact.armadura", "use_item": true, "play_sounds": "armor.equip_leather"}]}}, "minecraft:removearmor": {"minecraft:spell_effects": {"remove_effects": ["resistance", "speed", "strenght", "jump", "night_vision", "regeneration", "absorption"]}, "minecraft:attack": {"damage": 9}}, "minecraft:black_scarf": {"minecraft:is_saddled": {}, "minecraft:variant": {"value": 0}}, "minecraft:blue_scarf": {"minecraft:is_saddled": {}, "minecraft:variant": {"value": 1}}, "minecraft:brown_scarf": {"minecraft:is_saddled": {}, "minecraft:variant": {"value": 2}}, "minecraft:cian_scarf": {"minecraft:is_saddled": {}, "minecraft:variant": {"value": 3}}, "minecraft:gray_scarf": {"minecraft:is_saddled": {}, "minecraft:variant": {"value": 4}}, "minecraft:green_scarf": {"minecraft:is_saddled": {}, "minecraft:variant": {"value": 5}}, "minecraft:light_blue_scarf": {"minecraft:is_saddled": {}, "minecraft:variant": {"value": 6}}, "minecraft:light_gray_scarf": {"minecraft:is_saddled": {}, "minecraft:variant": {"value": 7}}, "minecraft:lime_scarf": {"minecraft:is_saddled": {}, "minecraft:variant": {"value": 8}}, "minecraft:magenta_scarf": {"minecraft:is_saddled": {}, "minecraft:variant": {"value": 9}}, "minecraft:orange_scarf": {"minecraft:is_saddled": {}, "minecraft:variant": {"value": 10}}, "minecraft:pink_scarf": {"minecraft:is_saddled": {}, "minecraft:variant": {"value": 11}}, "minecraft:purple_scarf": {"minecraft:is_saddled": {}, "minecraft:variant": {"value": 12}}, "minecraft:red_scarf": {"minecraft:is_saddled": {}, "minecraft:variant": {"value": 13}}, "minecraft:white_scarf": {"minecraft:is_saddled": {}, "minecraft:variant": {"value": 14}}, "minecraft:yellow_scarf": {"minecraft:is_saddled": {}, "minecraft:variant": {"value": 15}}, "minecraft:caracal_baby": {"minecraft:is_baby": {}, "minecraft:scale": {"value": 0.5}, "minecraft:ageable": {"duration": 1200, "feed_items": ["chicken", "cooked_chicken", "beef", "cooked_beef", "muttonRaw", "muttonCooked", "porkchop", "cooked_porkchop", "rabbit", "cooked_rabbit", "rotten_flesh"], "grow_up": {"event": "minecraft:ageable_grow_up", "target": "self"}}}, "minecraft:caracal_adult": {"minecraft:experience_reward": {"on_bred": "<PERSON><PERSON>(1,7)", "on_death": "query.last_hit_by_player ? Math.Random(1,3) : 0"}, "minecraft:scale": {"value": 1.0}, "minecraft:loot": {"table": "loot_tables/entities/caracal.json"}, "minecraft:breedable": {"require_tame": true, "breeds_with": {"mate_type": "worldanimals:caracal", "baby_type": "worldanimals:caracal", "breed_event": {"event": "minecraft:entity_born", "target": "baby"}}, "breed_items": ["minecraft:salmon", "minecraft:cod", "minecraft:tropical_fish"]}}, "minecraft:caracal_angry": {"minecraft:angry": {"duration": 25, "broadcast_anger": true, "broadcast_range": 20, "calm_event": {"event": "minecraft:on_calm", "target": "self"}}, "minecraft:on_target_acquired": {}}, "minecraft:caracal_wild": {"minecraft:behavior.avoid_mob_type": {"priority": 3, "entity_types": [{"filters": {"test": "is_family", "subject": "other", "value": "llama"}, "max_dist": 24, "walk_speed_multiplier": 1.5, "sprint_speed_multiplier": 1.5}], "probability_per_strength": 0.14}, "minecraft:tameable": {"probability": 1.0, "tame_items": "worldanimals:collar", "tame_event": {"event": "minecraft:on_tame", "target": "self"}}, "minecraft:behavior.nearest_attackable_target": {"priority": 4, "attack_interval": 10, "reselect_targets": true, "entity_types": [{"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "skeleton"}, {"test": "is_family", "subject": "other", "value": "zombie"}]}, "max_dist": 16}, {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "baby_turtle"}, {"test": "in_water", "subject": "other", "operator": "!=", "value": true}]}, "max_dist": 16}, {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "skeleton"}, {"test": "is_underwater", "subject": "other", "operator": "!=", "value": true}]}, "max_dist": 16}], "must_see": true}, "minecraft:on_target_acquired": {"event": "minecraft:become_angry", "target": "self"}}, "minecraft:caracal_tame": {"minecraft:is_tamed": {}, "minecraft:sittable": {}, "minecraft:behavior.follow_owner": {"priority": 6, "speed_multiplier": 1.0, "start_distance": 10, "stop_distance": 2}, "minecraft:health": {"value": 80, "max": 80}, "minecraft:attack": {"damage": 9}, "minecraft:behavior.breed": {"priority": 7}, "minecraft:behavior.owner_hurt_by_target": {"priority": 1}, "minecraft:behavior.owner_hurt_target": {"priority": 2}, "minecraft:behavior.nearest_attackable_target": {"priority": 5, "attack_interval": 10, "entity_types": [{"filters": {"test": "is_family", "subject": "other", "value": "skeleton"}, "max_dist": 16}], "must_see": true}, "minecraft:leashable": {"soft_distance": 4.0, "hard_distance": 6.0, "max_distance": 10.0, "on_leash": {"event": "minecraft:on_leash", "target": "self"}, "on_unleash": {"event": "minecraft:on_unleash", "target": "self"}}}}, "components": {"minecraft:nameable": {}, "minecraft:type_family": {"family": ["caracal", "mob"]}, "minecraft:breathable": {"total_supply": 15, "suffocate_time": 0}, "minecraft:collision_box": {"width": 0.6, "height": 0.8}, "minecraft:health": {"value": 50, "max": 50}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:movement": {"value": 0.3}, "minecraft:navigation.walk": {"can_path_over_water": true, "avoid_damage_blocks": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:attack": {"damage": 9}, "minecraft:healable": {"items": [{"item": "minecraft:tropical_fish", "heal_amount": 10}, {"item": "minecraft:cod", "heal_amount": 10}, {"item": "minecraft:salmon", "heal_amount": 10}]}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.mount_pathing": {"priority": 1, "speed_multiplier": 1.25, "target_dist": 0, "track_target": true}, "minecraft:behavior.stay_while_sitting": {"priority": 3}, "minecraft:behavior.leap_at_target": {"priority": 4, "target_dist": 0.4}, "minecraft:behavior.melee_attack": {"priority": 5, "target_dist": 1.2, "track_target": true, "reach_multiplier": 1.0}, "minecraft:behavior.random_stroll": {"priority": 8, "speed_multiplier": 1.0}, "minecraft:behavior.look_at_player": {"priority": 6, "target_distance": 6.0, "probability": 0.02}, "minecraft:behavior.beg": {"priority": 9, "look_distance": 8, "look_time": [2, 4], "items": ["minecraft:cod", "minecraft:salmon", "minecraft:tropical_fish"]}, "minecraft:behavior.hurt_by_target": {"priority": 3}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}}, "events": {"minecraft:entity_spawned": {"randomize": [{"weight": 9, "remove": {}, "add": {"component_groups": ["minecraft:caracal_adult", "minecraft:caracal_wild"]}}, {"weight": 1, "remove": {}, "add": {"component_groups": ["minecraft:caracal_baby", "minecraft:caracal_wild"]}}]}, "minecraft:entity_born": {"remove": {}, "add": {"component_groups": ["minecraft:caracal_baby", "minecraft:caracal_tame", "minecraft:interacciones"]}}, "minecraft:ageable_grow_up": {"remove": {"component_groups": ["minecraft:caracal_baby"]}, "add": {"component_groups": ["minecraft:caracal_adult"]}}, "minecraft:ageable_set_baby": {"remove": {"component_groups": ["minecraft:caracal_adult"]}, "add": {"component_groups": ["minecraft:caracal_baby"]}}, "minecraft:on_tame": {"remove": {"component_groups": ["minecraft:caracal_wild"]}, "add": {"component_groups": ["minecraft:caracal_tame", "minecraft:interacciones"]}}, "minecraft:become_angry": {"remove": {"component_groups": ["minecraft:caracal_wild"]}, "add": {"component_groups": ["minecraft:caracal_angry"]}}, "minecraft:on_calm": {"remove": {"component_groups": ["minecraft:caracal_angry"]}, "add": {"component_groups": ["minecraft:caracal_wild"]}}, "minecraft:removearmor": {"remove": {"component_groups": ["minecraft:black_scarf"]}, "add": {"component_groups": ["minecraft:removearmor", "minecraft:caracal_tame", "minecraft:interacciones"]}}, "minecraft:black_scarf_on": {"remove": {"component_groups": ["minecraft:removearmor", "minecraft:interacciones"]}, "add": {"component_groups": ["minecraft:black_scarf"]}}, "minecraft:blue_scarf_on": {"remove": {"component_groups": ["minecraft:removearmor", "minecraft:interacciones"]}, "add": {"component_groups": ["minecraft:blue_scarf"]}}, "minecraft:brown_scarf_on": {"remove": {"component_groups": ["minecraft:removearmor", "minecraft:interacciones"]}, "add": {"component_groups": ["minecraft:brown_scarf"]}}, "minecraft:cian_scarf_on": {"remove": {"component_groups": ["minecraft:removearmor", "minecraft:interacciones"]}, "add": {"component_groups": ["minecraft:cian_scarf"]}}, "minecraft:gray_scarf_on": {"remove": {"component_groups": ["minecraft:removearmor", "minecraft:interacciones"]}, "add": {"component_groups": ["minecraft:gray_scarf"]}}, "minecraft:green_scarf_on": {"remove": {"component_groups": ["minecraft:removearmor", "minecraft:interacciones"]}, "add": {"component_groups": ["minecraft:green_scarf"]}}, "minecraft:light_blue_scarf_on": {"remove": {"component_groups": ["minecraft:removearmor", "minecraft:interacciones"]}, "add": {"component_groups": ["minecraft:light_blue_scarf"]}}, "minecraft:light_gray_scarf_on": {"remove": {"component_groups": ["minecraft:removearmor", "minecraft:interacciones"]}, "add": {"component_groups": ["minecraft:light_gray_scarf"]}}, "minecraft:lime_scarf_on": {"remove": {"component_groups": ["minecraft:removearmor", "minecraft:interacciones"]}, "add": {"component_groups": ["minecraft:lime_scarf"]}}, "minecraft:magenta_scarf_on": {"remove": {"component_groups": ["minecraft:removearmor", "minecraft:interacciones"]}, "add": {"component_groups": ["minecraft:magenta_scarf"]}}, "minecraft:orange_scarf_on": {"remove": {"component_groups": ["minecraft:removearmor", "minecraft:interacciones"]}, "add": {"component_groups": ["minecraft:orange_scarf"]}}, "minecraft:pink_scarf_on": {"remove": {"component_groups": ["minecraft:removearmor", "minecraft:interacciones"]}, "add": {"component_groups": ["minecraft:pink_scarf"]}}, "minecraft:purple_scarf_on": {"remove": {"component_groups": ["minecraft:removearmor", "minecraft:interacciones"]}, "add": {"component_groups": ["minecraft:purple_scarf"]}}, "minecraft:red_scarf_on": {"remove": {"component_groups": ["minecraft:removearmor", "minecraft:interacciones"]}, "add": {"component_groups": ["minecraft:red_scarf"]}}, "minecraft:white_scarf_on": {"remove": {"component_groups": ["minecraft:removearmor", "minecraft:interacciones"]}, "add": {"component_groups": ["minecraft:white_scarf"]}}, "minecraft:yellow_scarf_on": {"remove": {"component_groups": ["minecraft:removearmor", "minecraft:interacciones"]}, "add": {"component_groups": ["minecraft:yellow_scarf"]}}}}}