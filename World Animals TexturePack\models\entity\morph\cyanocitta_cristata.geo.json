{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.cyanocitta_cristata", "texture_width": 64, "texture_height": 64, "visible_bounds_width": 3, "visible_bounds_height": 2.5, "visible_bounds_offset": [0, 0.75, 0]}, "bones": [{"name": "into_morph", "pivot": [0, 0, 0]}, {"name": "dove", "parent": "into_morph", "pivot": [0, 0, 0]}, {"name": "body", "parent": "dove", "pivot": [0, 2.5, 1], "cubes": [{"origin": [-2, 2.5, -5], "size": [4, 4, 5], "pivot": [0, 3.5, 0], "rotation": [-42.5, 0, 0], "uv": [15, 3]}, {"origin": [-1.5, 2.95, -0.3], "size": [3, 3, 3], "inflate": 0.25, "pivot": [0, 3.5, 0], "rotation": [-12.5, 0, 0], "uv": [30, 9]}, {"origin": [-1.5, 2.15, 2.6], "size": [3, 3, 2], "pivot": [0, 3.5, 0], "rotation": [2.5, 0, 0], "uv": [0, 31]}]}, {"name": "head", "parent": "body", "pivot": [0, 6.5, -3], "cubes": [{"origin": [-1.5, 6.6, -4.3], "size": [3, 4, 3], "pivot": [0, 6.5, -3], "rotation": [17.5, 0, 0], "uv": [22, 28]}, {"origin": [-1, 4.7, -3.3], "size": [1, 6, 4], "pivot": [0, 6.5, -3], "rotation": [27.5, 0, 0], "uv": [14, 19]}, {"origin": [-1, 7, -0.625], "size": [1, 3, 5], "inflate": -0.1, "pivot": [0, 6.5, -3], "rotation": [70, 0, 0], "uv": [40, 0]}, {"origin": [-1.5, 5, -2.75], "size": [3, 5, 3], "inflate": -0.25, "pivot": [0, 6.5, -3], "rotation": [37.5, 0, 0], "uv": [28, 0]}]}, {"name": "boca0", "parent": "head", "pivot": [0, 8.57418, -4.86542], "cubes": [{"origin": [-0.5, 8, -7], "size": [1, 1, 3], "inflate": -0.2, "pivot": [0, 6.8, -3], "rotation": [17.5, 0, 0], "uv": [31, 32]}]}, {"name": "boca1", "parent": "head", "pivot": [0, 7.543, -4.66828], "cubes": [{"origin": [-0.5, 6.84305, -6.76828], "size": [1, 1, 3], "inflate": -0.3, "pivot": [0, 7.24305, -5.26828], "rotation": [17.5, 0, 0], "uv": [13, 30]}]}, {"name": "tail", "parent": "body", "pivot": [0, 3.8, 4.5], "rotation": [12.5, 0, 0], "cubes": [{"origin": [-1.5, 4.23288, 4.54741], "size": [3, 1, 5], "uv": [0, 25]}, {"origin": [-4.475, 4.23288, 2.54741], "size": [3, 1, 7], "inflate": -0.025, "pivot": [-1.5, 4.23288, 9.54741], "rotation": [0, -22.5, 0], "uv": [0, 8]}, {"origin": [1.45, 4.23288, 2.54741], "size": [3, 1, 7], "inflate": -0.025, "pivot": [1.5, 4.23288, 9.54741], "rotation": [0, 22.5, 0], "uv": [0, 0]}]}, {"name": "alas1", "parent": "body", "pivot": [2, 7.2, -2.6], "rotation": [47.5, 0, 0]}, {"name": "ala1_1", "parent": "alas1", "pivot": [2, 7.2, -2.6], "rotation": [0, 0, 65], "cubes": [{"origin": [1.65, 6.65, -5.1], "size": [4, 1, 5], "inflate": -0.4, "uv": [0, 16]}]}, {"name": "ala1_2", "parent": "ala1_1", "pivot": [5.35, 7.15, -3.1], "cubes": [{"origin": [4.85, 6.65, -5.1], "size": [5, 1, 4], "inflate": -0.4, "uv": [25, 23]}]}, {"name": "alas0", "parent": "body", "pivot": [-2, 7.2, -2.6], "rotation": [47.5, 0, 0]}, {"name": "ala0_0", "parent": "alas0", "pivot": [-2, 7.2, -2.6], "rotation": [0, 0, -65], "cubes": [{"origin": [-5.65, 6.65, -5.1], "size": [4, 1, 5], "inflate": -0.4, "uv": [15, 12]}]}, {"name": "ala0_1", "parent": "ala0_0", "pivot": [-5.35, 7.15, -3.1], "cubes": [{"origin": [-9.85, 6.65, -5.1], "size": [5, 1, 4], "inflate": -0.4, "uv": [20, 18]}]}, {"name": "leg1", "parent": "dove", "pivot": [-1, 3, 1], "cubes": [{"origin": [-1.5, 1, 0.5], "size": [1, 2, 1], "inflate": -0.225, "pivot": [0, 2, 1], "rotation": [-27.5, 0, 0], "uv": [0, 16]}, {"origin": [-1.5, -0.25, 0.3], "size": [1, 2, 1], "inflate": -0.25, "pivot": [0, 0.75, 0.8], "rotation": [12.5, 0, 0], "uv": [13, 3]}, {"origin": [-1.4, -0.325, -0.75], "size": [1, 1, 2], "inflate": -0.3, "pivot": [-1, 0.175, 1.5], "rotation": [0, -7.5, 0], "uv": [0, 11]}, {"origin": [-1.6, -0.325, -0.75], "size": [1, 1, 2], "inflate": -0.3, "pivot": [-1, 0.175, 1.5], "rotation": [0, 7.5, 0], "uv": [0, 8]}, {"origin": [-1, -0.325, -0.1], "size": [1, 1, 2], "inflate": -0.35, "pivot": [-1, 0.175, 1.5], "rotation": [0, 82.5, 0], "uv": [0, 8]}, {"origin": [-1.5, 2.4, 0.5], "size": [1, 1, 1], "inflate": 0.3, "uv": [16, 5]}]}, {"name": "leg0", "parent": "dove", "pivot": [1, 3, 1], "cubes": [{"origin": [0.5, 1, 0.5], "size": [1, 2, 1], "inflate": -0.225, "pivot": [0, 2, 1], "rotation": [-27.5, 0, 0], "uv": [13, 12]}, {"origin": [0.5, -0.25, 0.3], "size": [1, 2, 1], "inflate": -0.25, "pivot": [0, 0.75, 0.8], "rotation": [12.5, 0, 0], "uv": [13, 0]}, {"origin": [0.6, -0.325, -0.75], "size": [1, 1, 2], "inflate": -0.3, "pivot": [1, 0.175, 1.5], "rotation": [0, -7.5, 0], "uv": [0, 3]}, {"origin": [0, -0.325, 0], "size": [1, 1, 2], "inflate": -0.35, "pivot": [1, 0.175, 1.5], "rotation": [0, -82.5, 0], "uv": [0, 3]}, {"origin": [0.4, -0.325, -0.75], "size": [1, 1, 2], "inflate": -0.3, "pivot": [1, 0.175, 1.5], "rotation": [0, 7.5, 0], "uv": [0, 0]}, {"origin": [0.5, 2.4, 0.5], "size": [1, 1, 1], "inflate": 0.3, "uv": [16, 2]}]}, {"name": "root", "pivot": [0, 0, 0]}, {"name": "rotation", "parent": "root", "pivot": [0, 0, 0]}, {"name": "waist", "parent": "rotation", "pivot": [0, 12, 0]}, {"name": "body_human", "parent": "waist", "pivot": [0, 24, 0]}, {"name": "leftArm", "parent": "body_human", "pivot": [5, 22, 0]}, {"name": "la", "parent": "leftArm", "pivot": [6, 18, 0]}, {"name": "leftItem", "parent": "la", "pivot": [6, 15, 1]}, {"name": "rightArm", "parent": "body_human", "pivot": [-5, 22, 0]}, {"name": "ra", "parent": "rightArm", "pivot": [-6, 18, 0]}, {"name": "rightItem", "parent": "ra", "pivot": [-6, 17, 2]}]}]}