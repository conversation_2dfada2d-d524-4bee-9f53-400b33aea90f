{"format_version": "1.13.0", "minecraft:entity": {"description": {"identifier": "worldanimals:eagle", "is_spawnable": true, "is_summonable": true, "is_experimental": false, "animations": {"bird": "animation.bird"}, "scripts": {"animate": ["bird"]}}, "component_groups": {"fly_on": {"minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:mark_variant": {"value": 0}, "minecraft:physics": {"has_gravity": false}, "minecraft:timer": {"looping": false, "time": 20.1, "randomInterval": false, "time_down_event": {"event": "minecraft:fly_off"}}, "minecraft:movement": {"value": 0.35}, "minecraft:movement.glide": {"start_speed": 0.35, "speed_when_turning": 0.45}, "minecraft:follow_range": {"value": 64, "max": 64}, "minecraft:behavior.swoop_attack": {"priority": 2, "delay_range": [10.0, 20.0]}, "minecraft:behavior.circle_around_anchor": {"priority": 3, "radius_range": [5.0, 15.0], "radius_change_chance": 250, "height_above_target_range": [20.0, 40.0], "height_offset_range": [-4.0, 5.0], "height_change_chance": 350, "goal_radius": 1.0}}, "fly_off": {"minecraft:jump.static": {}, "minecraft:mark_variant": {"value": 1}, "minecraft:timer": {"looping": false, "time": 20.1, "randomInterval": false, "time_down_event": {"event": "minecraft:fly_on"}}, "minecraft:navigation.walk": {"can_path_over_water": true, "avoid_damage_blocks": true}, "minecraft:movement.basic": {}, "minecraft:can_climb": {}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.panic": {"priority": 1, "speed_multiplier": 1.5}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}}, "minecraft:eagle_baby": {"minecraft:is_baby": {}, "minecraft:variant": {"value": 1}, "minecraft:scale": {"value": 0.5}, "minecraft:ageable": {"duration": 1200, "feed_items": ["wheat_seeds", "beetroot_seeds", "melon_seeds", "pumpkin_seeds"], "grow_up": {"event": "minecraft:ageable_grow_up", "target": "self"}}, "minecraft:behavior.follow_parent": {"priority": 5, "speed_multiplier": 1.1}}, "minecraft:eagle_adult": {"minecraft:variant": {"value": 0}, "minecraft:experience_reward": {"on_bred": "<PERSON><PERSON>(1,7)", "on_death": "query.last_hit_by_player ? Math.Random(1,3) : 0"}, "minecraft:loot": {"table": "loot_tables/entities/eagle.json"}, "minecraft:breedable": {"require_tame": false, "breeds_with": {"mate_type": "worldanimals:eagle", "baby_type": "worldanimals:eagle", "breed_event": {"event": "minecraft:entity_born", "target": "baby"}}, "breed_items": ["wheat_seeds", "beetroot_seeds", "melon_seeds", "pumpkin_seeds"]}, "minecraft:behavior.breed": {"priority": 3, "speed_multiplier": 1.0}, "minecraft:rideable": {"seat_count": 1, "family_types": ["zombie"], "seats": {"position": [0.0, 0.4, 0.0]}}}}, "components": {"minecraft:type_family": {"family": ["eagle", "mob"]}, "minecraft:breathable": {"total_supply": 15, "suffocate_time": 0}, "minecraft:collision_box": {"width": 0.6, "height": 0.8}, "minecraft:nameable": {}, "minecraft:health": {"value": 12, "max": 12}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:movement": {"value": 0.25}, "minecraft:damage_sensor": {"triggers": {"cause": "fall", "deals_damage": false}}, "minecraft:leashable": {"soft_distance": 4.0, "hard_distance": 6.0, "max_distance": 10.0}, "minecraft:balloonable": {"mass": 0.6}, "minecraft:behavior.mount_pathing": {"priority": 2, "speed_multiplier": 1.5, "target_dist": 0.0, "track_target": true}, "minecraft:behavior.tempt": {"priority": 4, "speed_multiplier": 1.0, "items": ["wheat_seeds", "beetroot_seeds", "melon_seeds", "pumpkin_seeds"]}, "minecraft:behavior.random_stroll": {"priority": 6, "speed_multiplier": 1.0}, "minecraft:behavior.look_at_player": {"priority": 7, "look_distance": 6.0, "probability": 0.02}, "minecraft:behavior.random_look_around": {"priority": 8}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}}, "events": {"from_egg": {"add": {"component_groups": ["minecraft:eagle_baby"]}}, "minecraft:entity_spawned": {"randomize": [{"weight": 95, "remove": {}, "add": {"component_groups": ["minecraft:eagle_adult", "fly_off"]}}, {"weight": 5, "remove": {}, "add": {"component_groups": ["minecraft:eagle_baby"]}}]}, "minecraft:entity_born": {"remove": {}, "add": {"component_groups": ["minecraft:eagle_baby"]}}, "minecraft:ageable_grow_up": {"remove": {"component_groups": ["minecraft:eagle_baby"]}, "add": {"component_groups": ["minecraft:eagle_adult", "fly_off"]}}, "minecraft:fly_off": {"add": {"component_groups": ["fly_off"]}, "remove": {"component_groups": ["fly_on"]}}, "minecraft:fly_on": {"add": {"component_groups": ["fly_on"]}, "remove": {"component_groups": ["fly_off"]}}}}}