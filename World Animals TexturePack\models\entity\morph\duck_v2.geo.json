{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.duck_v2", "texture_width": 64, "texture_height": 64, "visible_bounds_width": 3, "visible_bounds_height": 2.5, "visible_bounds_offset": [0, 0.75, 0]}, "bones": [{"name": "into_morph", "pivot": [0, 0, 0]}, {"name": "duck", "parent": "into_morph", "pivot": [0, 0, 0]}, {"name": "body", "parent": "duck", "pivot": [0, 3, 0], "cubes": [{"origin": [-3, 3, -4], "size": [6, 6, 8], "uv": [0, 0]}, {"origin": [-2, 7.3, -4], "size": [4, 2, 5], "pivot": [0, 9, -1.5], "rotation": [7.5, 0, 0], "uv": [0, 47]}, {"origin": [-2, 7.95, 0.8], "size": [4, 2, 3], "inflate": -0.05, "pivot": [0, 9, -1.5], "rotation": [-7.5, 0, 0], "uv": [35, 42]}, {"origin": [-2.5, 3, -8], "size": [5, 5, 4], "pivot": [0, 3, -4], "rotation": [-32.5, 0, 0], "uv": [0, 32]}]}, {"name": "head", "parent": "body", "pivot": [0, 5.75, -5.25], "cubes": [{"origin": [-2, 5.75, -7.85], "size": [4, 4, 4], "pivot": [0, 7.65, -5.35], "rotation": [15, 0, 0], "uv": [34, 34]}, {"origin": [-1.5, 9.15, -7.85], "size": [3, 3, 3], "uv": [17, 24]}]}, {"name": "cabeza", "parent": "head", "pivot": [0, 11.8, -6.5], "rotation": [15, 0, 0], "cubes": [{"origin": [-1.5, 11.95, -8.85], "size": [3, 4, 4], "inflate": 0.25, "uv": [40, 17]}]}, {"name": "pico1", "parent": "cabeza", "pivot": [0, 12.9, -9.1], "cubes": [{"origin": [-1, 11.95, -13], "size": [2, 1, 4], "uv": [40, 25]}]}, {"name": "pico0", "parent": "cabeza", "pivot": [0, 14.05, -9.1], "cubes": [{"origin": [-1, 12.925, -14], "size": [2, 1, 5], "inflate": 0.1, "uv": [0, 41]}, {"origin": [-1, 13.15, -13.3], "size": [2, 1, 5], "inflate": -0.15, "pivot": [0, 14.05, -11.5], "rotation": [17.5, 0, 0], "uv": [37, 0]}]}, {"name": "tail", "parent": "body", "pivot": [0, 9, 4], "rotation": [-25, 0, 0], "cubes": [{"origin": [-2.5, 5, 4], "size": [5, 4, 6], "uv": [24, 24]}, {"origin": [-3.2, 5, 4], "size": [3, 4, 5], "inflate": -0.025, "pivot": [-2.5, 9, 4], "rotation": [0, -22.5, 0], "uv": [18, 34]}, {"origin": [0.2, 5, 4], "size": [3, 4, 5], "inflate": -0.025, "pivot": [2.5, 9, 4], "rotation": [0, 22.5, 0], "uv": [32, 8]}]}, {"name": "alas1", "parent": "body", "pivot": [-3, 9, 0], "rotation": [0, 180, 0]}, {"name": "ala1_1", "parent": "alas1", "pivot": [-3, 9, -1.5], "rotation": [0, 0, 65], "cubes": [{"origin": [-3.35, 8.45, -4], "size": [4, 1, 8], "inflate": -0.4, "uv": [16, 15]}]}, {"name": "ala1_2", "parent": "ala1_1", "pivot": [0.35, 8.95, -2], "cubes": [{"origin": [-0.15, 8.45, -4], "size": [5, 1, 7], "inflate": -0.4, "uv": [0, 24]}]}, {"name": "alas0", "parent": "body", "pivot": [3, 9, 0], "rotation": [0, 180, 0]}, {"name": "ala0_0", "parent": "alas0", "pivot": [3, 9, 0], "rotation": [0, 0, -65], "cubes": [{"origin": [-0.65, 8.45, -4], "size": [4, 1, 8], "inflate": -0.4, "uv": [0, 14]}]}, {"name": "ala0_1", "parent": "ala0_0", "pivot": [-0.35, 8.95, -0.5], "cubes": [{"origin": [-4.85, 8.45, -4], "size": [5, 1, 7], "inflate": -0.4, "uv": [20, 0]}]}, {"name": "leg0", "parent": "duck", "pivot": [-2, 3, 0.25], "cubes": [{"origin": [-3.5, -0.25, -2], "size": [3, 1, 3], "inflate": -0.25, "uv": [9, 41]}, {"origin": [-2.5, -0.2, -0.25], "size": [1, 4, 1], "inflate": -0.25, "uv": [4, 0]}]}, {"name": "leg1", "parent": "duck", "pivot": [2, 3, 0.25], "cubes": [{"origin": [0.5, -0.25, -2], "size": [3, 1, 3], "inflate": -0.25, "uv": [32, 17]}, {"origin": [1.5, -0.2, -0.25], "size": [1, 4, 1], "inflate": -0.25, "uv": [0, 0]}]}, {"name": "root", "pivot": [0, 0, 0]}, {"name": "rotation", "parent": "root", "pivot": [0, 0, 0]}, {"name": "waist2", "parent": "rotation", "pivot": [0, 12, 0]}, {"name": "body_human", "parent": "waist2", "pivot": [0, 24, 0]}, {"name": "leftArm", "parent": "body_human", "pivot": [5, 22, 0]}, {"name": "la", "parent": "leftArm", "pivot": [6, 18, 0]}, {"name": "leftItem", "parent": "la", "pivot": [6, 15, 1]}, {"name": "rightArm", "parent": "body_human", "pivot": [-5, 22, 0]}, {"name": "ra", "parent": "rightArm", "pivot": [-6, 18, 0]}, {"name": "rightItem", "parent": "ra", "pivot": [-6, 17, 2]}]}]}