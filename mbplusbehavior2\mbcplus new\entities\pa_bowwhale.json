{"format_version": "1.12.0", "minecraft:entity": {"description": {"identifier": "pa:bowwhale", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"minecraft:baby": {"minecraft:type_family": {"family": ["turtle", "baby_turtle"]}, "minecraft:collision_box": {"width": 0.6, "height": 0.2}, "minecraft:underwater_movement": {"value": 0.12}, "minecraft:is_baby": {}, "minecraft:scale": {"value": 0.16}, "minecraft:behavior.move_to_water": {"priority": 1, "search_range": 15, "search_height": 5, "goal_radius": 0.1}, "minecraft:ageable": {"duration": 1200, "feedItems": ["seagrass"], "drop_items": ["turtle_shell_piece"], "grow_up": {"event": "minecraft:ageable_grow_up", "target": "self"}}}, "minecraft:adult": {"minecraft:type_family": {"family": ["turtle"]}, "minecraft:collision_box": {"width": 1.2, "height": 0.4}, "minecraft:underwater_movement": {"value": 0.12}, "minecraft:loot": {"table": "loot_tables/entities/sea_turtle.json"}, "minecraft:breedable": {"requireTame": false, "causes_pregnancy": true, "breedsWith": {"mateType": "minecraft:turtle", "babyType": "minecraft:turtle", "breed_event": {"event": "minecraft:become_pregnant", "target": "self"}}, "breedItems": ["seagrass"]}, "minecraft:behavior.breed": {"priority": 2, "speed_multiplier": 1.0}, "minecraft:behavior.move_to_land": {"priority": 6, "search_range": 16, "search_height": 5, "goal_radius": 0.5}, "minecraft:behavior.random_stroll": {"priority": 9, "interval": 100}}, "minecraft:pregnant": {"minecraft:behavior.go_home": {"priority": 1, "speed_multiplier": 1.0, "interval": 700, "goal_radius": 4.0, "on_home": {"event": "minecraft:go_lay_egg", "target": "self"}}}, "minecraft:wants_to_lay_egg": {"minecraft:behavior.lay_egg": {"priority": 1, "speed_multiplier": 1.0, "search_range": 16, "search_height": 4, "goal_radius": 1.0, "on_lay": {"event": "minecraft:laid_egg", "target": "self"}}}}, "components": {"minecraft:breathable": {"totalSupply": 99999999, "suffocateTime": 0, "breathesWater": true, "breathesAir": true, "generatesBubbles": false}, "minecraft:health": {"value": 160, "max": 160}, "minecraft:movement": {"value": 0}, "minecraft:water_movement": {"drag_factor": 0.9}, "minecraft:navigation.generic": {"is_amphibious": true, "can_path_over_water": false, "can_swim": true, "can_walk": true, "can_sink": false, "avoid_damage_blocks": true}, "minecraft:movement.amphibious": {"max_turn": 5.0}, "minecraft:jump.static": {}, "minecraft:physics": {}, "minecraft:home": {}, "minecraft:follow_range": {"value": 1024}, "minecraft:behavior.hurt_by_target": {"priority": 3}, "minecraft:behavior.tempt": {"priority": 3, "speed_multiplier": 1.1, "items": ["seagrass"]}, "minecraft:behavior.move_to_water": {"priority": 4, "search_range": 16, "search_height": 5, "search_count": 1, "goal_radius": 0.1}, "minecraft:behavior.random_swim": {"priority": 7, "interval": 0, "xz_dist": 30, "y_dist": 15}, "minecraft:behavior.look_at_player": {"priority": 8, "target_distance": 6.0, "probability": 0.02}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 10 : 0"}, "minecraft:underwater_movement": {"value": 0.12}, "minecraft:type_family": {"family": ["whale", "shark"]}, "minecraft:scale": {"value": 2.5}, "minecraft:behavior.owner_hurt_by_target": {"priority": 1}, "minecraft:behavior.owner_hurt_target": {"priority": 2}, "minecraft:knockback_resistance": {"value": 2.6, "max": 2.6}, "minecraft:on_death": {"event": "on:death", "target": "self"}, "minecraft:on_hurt": {"event": "on:hurt", "target": "self"}, "minecraft:on_hurt_by_player": {"event": "on:hurt_by_player", "target": "self"}, "minecraft:on_ignite": {"event": "on:ignite", "target": "self"}, "minecraft:on_target_acquired": {"event": "on:target_acquired", "target": "self"}, "minecraft:on_target_escape": {"event": "on:target_escape", "target": "self"}, "minecraft:on_wake_with_owner": {"event": "on:wake_with_owner", "target": "self"}, "minecraft:behavior.avoid_block": {"priority": 9, "tick_interval": 30, "search_range": 30, "search_height": 30, "sprint_speed_modifier": 1.1, "target_selection_method": "nearest", "target_blocks": ["minecraft:rooted_dirt", "minecraft:cobblestone_wall", "minecraft:dirt", "minecraft:double_plant", "minecraft:grass", "minecraft:grass_path", "minecraft:red_sandstone", "minecraft:sand", "minecraft:sandstone", "minecraft:sandstone_stairs", "minecraft:seagrass", "minecraft:smooth_sandstone_stairs", "minecraft:stone_slab", "minecraft:stone_slab2", "minecraft:stone_slab4", "minecraft:tallgrass"], "avoid_block_sound": "retreat", "sound_interval": {"range_min": 2.0, "range_max": 5.0}}, "minecraft:behavior.panic": {"speed_multiplier": 1.25}}, "events": {"minecraft:entity_spawned": {"randomize": [{"weight": 9, "add": {"component_groups": ["minecraft:adult"]}}, {"weight": 1, "add": {"component_groups": ["minecraft:baby"]}}]}, "minecraft:entity_born": {"add": {"component_groups": ["minecraft:baby"]}}, "minecraft:ageable_grow_up": {"remove": {"component_groups": ["minecraft:baby"]}, "add": {"component_groups": ["minecraft:adult"]}}, "minecraft:become_pregnant": {"add": {"component_groups": ["minecraft:pregnant"]}}, "minecraft:go_lay_egg": {"add": {"component_groups": ["minecraft:wants_to_lay_egg"]}, "remove": {"component_groups": ["minecraft:pregnant"]}}, "minecraft:laid_egg": {"remove": {"component_groups": ["minecraft:wants_to_lay_egg"]}}, "on:death": {"run_command": {"command": []}}, "on:hurt": {"run_command": {"command": []}}, "on:hurt_by_player": {"run_command": {"command": []}}, "on:ignite": {"run_command": {"command": []}}, "on:target_acquired": {"run_command": {"command": []}}, "on:target_escape": {"run_command": {"command": []}}, "on:wake_with_owner": {"run_command": {"command": []}}}}}