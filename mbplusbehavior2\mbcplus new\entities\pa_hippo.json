{"format_version": "1.16.0", "minecraft:entity": {"description": {"identifier": "pa:hippo", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"attack_cooldown": {"minecraft:attack_cooldown": {"attack_cooldown_time": 120.0, "attack_cooldown_complete_event": {"event": "attack_cooldown_complete_event", "target": "self"}}}, "axolotl_lucy": {"minecraft:variant": {"value": 0}}, "axolotl_cyan": {"minecraft:variant": {"value": 1}}, "axolotl_gold": {"minecraft:variant": {"value": 2}}, "axolotl_wild": {"minecraft:variant": {"value": 3}}, "axolotl_blue": {"minecraft:variant": {"value": 4}}, "axolotl_baby": {"minecraft:is_baby": {}, "minecraft:scale": {"value": 0.5}, "minecraft:ageable": {"duration": 1200, "feed_items": "tropical_fish_bucket", "transform_to_item": "bucket:0", "grow_up": {"event": "minecraft:ageable_grow_up", "target": "self"}}, "minecraft:behavior.follow_parent": {"priority": 5, "speed_multiplier": 1.1}}, "axolotl_adult": {"minecraft:experience_reward": {"on_bred": "<PERSON><PERSON>(1,7)", "on_death": "query.last_hit_by_player ? 3 : 0"}, "minecraft:behavior.breed": {"priority": 1, "speed_multiplier": 1.0}, "minecraft:breedable": {"require_tame": false, "breed_items": "tropical_fish_bucket", "transform_to_item": "bucket:0", "breeds_with": {"mate_type": "minecraft:axolotl", "baby_type": "minecraft:axolotl", "breed_event": {"event": "minecraft:entity_born", "target": "baby"}}, "mutation_factor": {"variant": 0.00083}}}, "axolotl_in_water": {"minecraft:environment_sensor": {"triggers": [{"filters": {"test": "in_water", "operator": "!=", "value": true}, "event": "start_drying_out"}]}}, "axolotl_dried": {"minecraft:damage_over_time": {"damage_per_hurt": 1, "time_between_hurt": 0}}, "axolotl_on_land": {"minecraft:drying_out_timer": {"total_time": 300, "water_bottle_refill_time": 90, "dried_out_event": {"event": "dried_out"}, "stopped_drying_out_event": {"event": "stop_drying_out"}, "recover_after_dried_out_event": {"event": "recover_after_dried_out"}}}, "axolotl_on_land_in_rain": {"minecraft:environment_sensor": {"triggers": [{"filters": {"test": "in_water_or_rain", "operator": "!=", "value": true}, "event": "start_drying_out"}, {"filters": {"test": "in_water", "operator": "==", "value": true}, "event": "enter_water"}]}}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:type_family": {"family": ["hippo", "cow"]}, "minecraft:collision_box": {"height": 0.5, "width": 1.1}, "minecraft:breathable": {"breathesWater": true, "totalSupply": 99999999, "suffocateTime": -1, "generatesBubbles": true, "breathesAir": true}, "minecraft:health": {"value": 40, "max": 40}, "minecraft:damage_sensor": {"triggers": {"cause": "lightning", "deals_damage": true, "damage_multiplier": 2000.0}}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:navigation.generic": {"is_amphibious": true, "can_path_over_water": true, "can_swim": true, "can_walk": true, "can_sink": false, "avoid_damage_blocks": true}, "minecraft:movement.amphibious": {}, "minecraft:movement": {"value": 0.26}, "minecraft:underwater_movement": {"value": 0.12}, "minecraft:jump.static": {}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:leashable": {"soft_distance": 4.0, "hard_distance": 6.0, "max_distance": 10.0}, "minecraft:despawn": {"despawn_from_distance": {}}, "minecraft:attack": {"damage": 10}, "minecraft:combat_regeneration": {}, "minecraft:behavior.play_dead": {"priority": 0, "duration": 10, "force_below_health": 8, "random_start_chance": 0.33, "random_damage_range": [0, 2], "damage_sources": ["contact", "entity_attack", "entity_explosion", "magic", "projectile", "thorns", "wither"], "apply_regeneration": true, "filters": {"test": "in_water", "operator": "==", "value": true}}, "minecraft:behavior.tempt": {"priority": 2, "speed_multiplier": 1.1, "can_tempt_vertically": true, "items": ["tropical_fish_bucket"]}, "minecraft:behavior.move_to_water": {"priority": 6, "search_range": 16, "search_height": 5, "search_count": 1, "goal_radius": 0.1}, "minecraft:behavior.swim_idle": {"priority": 7, "idle_time": 5.0, "success_rate": 0.05}, "minecraft:behavior.random_swim": {"priority": 8, "interval": 0, "xz_dist": 30, "y_dist": 15}, "minecraft:behavior.random_stroll": {"priority": 9, "interval": 100}, "minecraft:behavior.look_at_player": {"priority": 10, "target_distance": 6.0, "probability": 0.02}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 10 : 0"}, "minecraft:scale": {"value": 1.2}, "minecraft:behavior.hurt_by_target": {"priority": 3}, "minecraft:behavior.owner_hurt_by_target": {"priority": 1}, "minecraft:behavior.owner_hurt_target": {"priority": 2}, "minecraft:on_death": {"event": "on:death", "target": "self"}, "minecraft:on_hurt": {"event": "on:hurt", "target": "self"}, "minecraft:on_hurt_by_player": {"event": "on:hurt_by_player", "target": "self"}, "minecraft:on_ignite": {"event": "on:ignite", "target": "self"}, "minecraft:on_target_acquired": {"event": "on:target_acquired", "target": "self"}, "minecraft:on_target_escape": {"event": "on:target_escape", "target": "self"}, "minecraft:on_wake_with_owner": {"event": "on:wake_with_owner", "target": "self"}, "minecraft:behavior.melee_attack": {"priority": 2, "speed_multiplier": 1.0, "target_dist": 0.0, "max_dist": 3, "random_stop_interval": 100, "track_target": false, "reach_multiplier": 1.4}, "minecraft:knockback_resistance": {"value": 1.0, "max": 1.0}}, "events": {"minecraft:entity_spawned": {"sequence": [{"add": {"component_groups": ["axolotl_adult", "axolotl_in_water"]}}, {"randomize": [{"weight": 25, "add": {"component_groups": ["axolotl_cyan"]}}, {"weight": 25, "add": {"component_groups": ["axolotl_gold"]}}, {"weight": 25, "add": {"component_groups": ["axolotl_lucy"]}}, {"weight": 25, "add": {"component_groups": ["axolotl_wild"]}}]}]}, "attack_cooldown_complete_event": {"remove": {"component_groups": ["attack_cooldown"]}}, "killed_enemy_event": {"add": {"component_groups": ["attack_cooldown"]}}, "minecraft:entity_born": {"sequence": [{"remove": {"component_groups": ["axolotl_adult"]}, "add": {"component_groups": ["axolotl_baby", "axolotl_in_water"]}}, {"filters": {"test": "has_component", "operator": "!=", "value": "minecraft:variant"}, "add": {"component_groups": ["axolotl_blue"]}}]}, "minecraft:ageable_grow_up": {"remove": {"component_groups": ["axolotl_baby"]}, "add": {"component_groups": ["axolotl_adult"]}}, "stop_drying_out": {"remove": {"component_groups": ["axolotl_on_land", "axolotl_dried"]}, "add": {"component_groups": ["axolotl_on_land_in_rain"]}}, "start_drying_out": {"remove": {"component_groups": ["axolotl_on_land_in_rain", "axolotl_in_water"]}, "add": {"component_groups": ["axolotl_on_land"]}}, "dried_out": {"add": {"component_groups": ["axolotl_dried"]}}, "recover_after_dried_out": {"remove": {"component_groups": ["axolotl_dried"]}}, "enter_water": {"remove": {"component_groups": ["axolotl_on_land", "axolotl_on_land_in_rain", "axolotl_dried"]}, "add": {"component_groups": ["axolotl_in_water"]}}, "on:death": {"run_command": {"command": []}}, "on:hurt": {"run_command": {"command": []}}, "on:hurt_by_player": {"run_command": {"command": []}}, "on:ignite": {"run_command": {"command": []}}, "on:target_acquired": {"run_command": {"command": []}}, "on:target_escape": {"run_command": {"command": []}}, "on:wake_with_owner": {"run_command": {"command": []}}}}}