{
  "format_version": "1.13.0",
  "minecraft:entity": {
    "description": {
      "identifier": "worldanimals:kangaroo",
      "is_spawnable": true,
      "is_summonable": true,
      "is_experimental": false
    },
    "component_groups": {

      "minecraft:kangaroo_baby": {
        "minecraft:experience_reward": {
          "on_death": "query.last_hit_by_player ? 12 + (query.equipment_count * Math.Random(1,3)) : 0"
        },
        "minecraft:is_baby": {},
        "minecraft:scale": {
          "value": 0.45
        },
        "minecraft:movement": {
          "value": 0.25
        }
      },

      "minecraft:kangaroo_adult": {
        "minecraft:scale": {
          "value": 0.9
        },
        "minecraft:experience_reward": {
          "on_death": "query.last_hit_by_player ? 5 + (query.equipment_count * Math.Random(1,3)) : 0"
        },
        "minecraft:movement": {
          "value": 0.35
        },
        "minecraft:behavior.mount_pathing": {
          "priority": 2,
          "speed_multiplier": 1.25,
          "target_dist": 0.0,
          "track_target": true
        }
      },

      "minecraft:kangaroo_jockey": {
        "minecraft:behavior.find_mount": {
          "priority": 1,
          "within_radius": 16,
          "start_delay": 15,
          "max_failed_attempts": 20
        }
      }
    },

    "components": {
      "minecraft:nameable": {
      },

      // kangaroo Components
      "minecraft:type_family": {
        "family": [ "kangaroo", "undead", "monster", "animal" ]
      },
      "minecraft:collision_box": {
        "width": 1.2,
        "height": 1.8
      },
      "minecraft:movement.basic": {
      },
      "minecraft:navigation.walk": {
        "is_amphibious": true,
        "can_pass_doors": true,
        "can_walk": true,
        "can_break_doors": true
      },
      "minecraft:jump.static": {
      },
      "minecraft:can_climb": {
      },
      "minecraft:health": {
        "value": 20,
        "max": 20
      },
        "minecraft:navigation.generic": {
          "is_amphibious": false,
          "can_path_over_water": false,
          "can_swim": true,
          "can_walk": true,
          "can_breach": false,
          "can_jump": false
        },
      "minecraft:hurt_on_condition": {
        "damage_conditions": [
          {
            "filters": { "test": "in_lava", "subject": "self", "operator": "==", "value": true },
            "cause": "lava",
            "damage_per_tick": 4
          }
        ]
      },
      "minecraft:breathable": {
        "total_supply": 15,
        "suffocate_time": 0,
        "breathes_air": true,
        "breathes_water": true
      },
      "minecraft:attack": {
        "damage": 14
      },
      "minecraft:loot": {
        "table": "loot_tables/entities/rabbit.json"
      },

      "minecraft:environment_sensor": {
        "triggers": {
          "filters": {
            "test": "is_underwater",
            "operator": "==",
            "value": true
          },
          "event": "minecraft:start_transforming"
        }
      },

      // kangaroo Behaviors
      "minecraft:behavior.melee_attack": {
        "priority": 3,
        "speed_multiplier": 1,
        "track_target": false
      },
      "minecraft:behavior.stomp_turtle_egg": {
        "priority": 4,
        "speed_multiplier": 1,
        "search_range": 10,
        "search_height": 3,
        "goal_radius": 1.14,
        "search_count": 4,
        "interval": 20
      },
      "minecraft:behavior.move_towards_restriction": {
        "priority": 5,
        "speed_multiplier": 1
      },
      "minecraft:behavior.random_stroll": {
        "priority": 6,
        "speed_multiplier": 1
      },
      "minecraft:behavior.look_at_player": {
        "priority": 7,
        "look_distance": 6,
        "probability": 0.02
      },
      "minecraft:behavior.random_look_around": {
        "priority": 7
      },
      "minecraft:behavior.hurt_by_target": {
        "priority": 1
      },
      "minecraft:behavior.nearest_attackable_target": {
        "priority": 2,
        "within_radius": 25,
        "reselect_targets": true,
        "entity_types": [
          {
            "filters": {
              "any_of": [
                { "test": "is_family", "subject": "other", "value": "player" },
                { "test": "is_family", "subject": "other", "value": "tiger" },
                { "test": "is_family", "subject": "other", "value": "leopard" }
              ]
            },
            "max_dist": 35
          },
          {
            "filters": {
              "any_of": [
                { "test": "is_family", "subject": "other", "value": "lion" },
                { "test": "is_family", "subject": "other", "value": "wandering_trader" }
              ]
            },
            "max_dist": 35,
            "must_see": false
          },
          {
            "filters": {
              "all_of": [
                { "test": "is_family", "subject": "other", "value": "baby_turtle" },
                { "test": "in_water", "subject": "other", "operator": "!=", "value": true }
              ]
            },
            "max_dist": 35
          }
        ],
        "must_see": true,
        "must_see_forget_duration": 17.0
      },
      "minecraft:physics": {
      },
      "minecraft:pushable": {
        "is_pushable": true,
        "is_pushable_by_piston": true
      }
    },

    "events": {
      "minecraft:entity_spawned": {
        "randomize": [
          {
            "weight": 380,
            "remove": {},
            "add": {
              "component_groups": [
                "minecraft:kangaroo_adult",
                "minecraft:can_have_equipment"
              ]
            }
          },
          {
            "weight": 17,
            "remove": {
            },
            "add": {
              "component_groups": [
                "minecraft:kangaroo_baby",
                "minecraft:can_have_equipment"
              ]

            }
          },
          {
            "weight": 3,
            "remove": {
            },
            "add": {
              "component_groups": [
                "minecraft:kangaroo_baby",
                "minecraft:kangaroo_jockey",
                "minecraft:can_have_equipment"
              ]
            }
          }
        ]
      },
      "minecraft:as_adult": {
        "add": {
          "component_groups": [
            "minecraft:kangaroo_adult"
          ]
        }
      },
      "minecraft:as_baby": {
        "add": {
          "component_groups": [
            "minecraft:kangaroo_baby"
          ]
        }
      },
      "minecraft:start_transforming": {
        "add": {
          "component_groups": [
            "minecraft:start_drowned_transformation"
          ]
        },
        "remove": {
          "component_groups": [ "minecraft:look_to_start_drowned_transformation" ]
        }
      },
      "minecraft:stop_transforming": {
        "add": {
          "component_groups": [
            "minecraft:look_to_start_drowned_transformation"
          ]
        },
        "remove": {
          "component_groups": [
            "minecraft:start_drowned_transformation"
          ]
        }
      },
      "minecraft:convert_to_drowned": {
        "sequence": [
          {
            "filters": {
              "test": "has_component",
              "operator": "!=",
              "value": "minecraft:is_baby"
            },
            "add": {
              "component_groups": [ "minecraft:convert_to_drowned" ]
            },
            "remove": {
              "component_groups": [ "minecraft:start_drowned_transformation" ]
            }
          },
          {
            "filters": {
              "test": "has_component",
              "value": "minecraft:is_baby"
            },
            "add": {
              "component_groups": [ "minecraft:convert_to_baby_drowned" ]
            },
            "remove": {
              "component_groups": [ "minecraft:start_drowned_transformation" ]
            }
          }
        ]
      }
    }
  }
}
