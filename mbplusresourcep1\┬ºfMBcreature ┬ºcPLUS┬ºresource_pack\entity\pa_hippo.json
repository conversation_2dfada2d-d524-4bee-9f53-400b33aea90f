{"format_version": "1.8.0", "minecraft:client_entity": {"description": {"identifier": "pa:hippo", "materials": {"default": "axolotl", "limbs": "axolotl_limbs"}, "textures": {"blue": "textures/entity/pamobile/pa_hippo", "cyan": "textures/entity/pamobile/pa_hippo", "gold": "textures/entity/pamobile/pa_hippo", "lucy": "textures/entity/pamobile/pa_hippo", "wild": "textures/entity/pamobile/pa_hippo"}, "geometry": {"default": "geometry.pa_hippo"}, "animations": {"idle_float": "animation.pa_hippo.idle_underwater", "idle_floor": "animation.pa_hippo.look_at_target", "idle_floor_water": "animation.pa_hippo.idle_floor_underwater", "swim": "animation.pa_hippo.swim", "walk_floor": "animation.pa_hippo.walk_floor", "walk_floor_water": "animation.pa_hippo.walk_floor_underwater", "play_dead": "animation.pa_hippo.play_dead", "swim_angle": "animation.pa_hippo.swim_angle", "look_at_target": "animation.pa_hippo.look_at_target"}, "scripts": {"pre_animation": ["variable.moving = query.ground_speed > 0 || query.vertical_speed > 0;", "variable.pitch = query.body_x_rotation;"]}, "animation_controllers": [{"general": "controller.animation.axolotl.general"}, {"move": "controller.animation.axolotl.move"}], "render_controllers": ["controller.render.axolotl"], "spawn_egg": {"texture": "pa:hippo", "texture_index": 0}}}}