{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.reptil_helmet", "texture_width": 64, "texture_height": 64, "visible_bounds_width": 2, "visible_bounds_height": 3.5, "visible_bounds_offset": [0, 1.25, 0]}, "bones": [{"name": "head", "pivot": [0, 24, 0], "mirror": true, "cubes": [{"origin": [-4, 23, -4], "size": [8, 1, 8], "inflate": 0.2, "uv": [18, 36]}, {"origin": [-4, 24, 4], "size": [8, 8, 1], "inflate": 0.3, "uv": [0, 36]}, {"origin": [-4, 31, -4], "size": [8, 2, 8], "inflate": 0.3, "uv": [0, 26]}, {"origin": [-5, 24, -4], "size": [1, 8, 8], "inflate": 0.2, "uv": [0, 0]}, {"origin": [-4, 27, -3], "size": [1, 4, 4], "uv": [7, 34]}, {"origin": [3, 27, -3], "size": [1, 4, 4], "uv": [7, 34]}, {"origin": [4, 24, -4], "size": [1, 8, 8], "inflate": 0.2, "uv": [0, 0]}]}, {"name": "boca", "parent": "head", "pivot": [0, 32, -4], "rotation": [-5, 0, 0], "cubes": [{"origin": [-5, 30, -9.8], "size": [10, 2, 6], "inflate": -0.1, "uv": [22, 4], "mirror": true}, {"origin": [-4, 30, -15.6], "size": [8, 2, 6], "inflate": -0.1, "uv": [0, 16], "mirror": true}, {"origin": [-4, 31, -11.9], "size": [8, 2, 8], "inflate": -0.2, "uv": [0, 26], "mirror": true}, {"origin": [-3, 30, -19.4], "size": [6, 2, 4], "inflate": -0.1, "uv": [0, 45], "mirror": true}, {"origin": [-4.5, 28.7, -6.5], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-4.5, 27.5, -6.5], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-4.5, 27.5, -5.3], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [3.5, 27.5, -6.5], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [3.5, 27.5, -5.3], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [3.5, 28.7, -5.3], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [3.5, 28.7, -6.5], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [3.5, 28.7, -7.7], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-0.5, 27.5, -18.9], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [1.5, 27.5, -18.9], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [0.5, 27.5, -18.9], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [0.5, 28.7, -18.9], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-0.5, 28.7, -18.9], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [1.5, 28.7, -18.9], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [1.5, 28.7, -17.7], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [1.5, 27.5, -17.7], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [1.5, 27.5, -16.5], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [1.5, 28.7, -16.5], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-2.5, 28.7, -17.7], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-2.5, 27.5, -17.7], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-4.5, 28.7, -5.3], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-4.5, 28.7, -7.7], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-4.5, 27.5, -7.7], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [3.5, 27.5, -8.9], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [3.5, 28.7, -8.9], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-4.5, 27.5, -8.9], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-4.5, 28.7, -8.9], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [2.5, 28.7, -10.3], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [2.5, 27.5, -10.3], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-3.5, 27.5, -10.3], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-3.5, 28.7, -10.3], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [2.5, 28.7, -11.5], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [2.5, 27.5, -11.5], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [2.5, 27.5, -12.7], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-3.5, 28.7, -12.7], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-3.5, 27.5, -12.7], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-3.5, 27.5, -11.5], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-3.5, 28.7, -11.5], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [2.5, 28.7, -12.7], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-3.5, 28.7, -13.9], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [2.5, 28.7, -13.9], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-2.5, 28.7, -15.3], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-2.5, 28.7, -16.5], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-3.5, 27.5, -13.9], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [2.5, 27.5, -13.9], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-2.5, 27.5, -15.3], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-2.5, 27.5, -16.5], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-2.5, 28.7, -18.9], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-1.5, 28.7, -18.9], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-2.5, 27.5, -18.9], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-1.5, 27.5, -18.9], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [1.5, 28.7, -15.3], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [1.5, 27.5, -15.3], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [3.5, 27.5, -7.7], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}]}, {"name": "boca2", "parent": "head", "pivot": [0, 24, -2], "rotation": [5, 0, 0], "cubes": [{"origin": [-5, 23, -9.9], "size": [10, 2, 8], "inflate": -0.1, "uv": [22, 16], "mirror": true}, {"origin": [-4, 23, -15.7], "size": [8, 2, 6], "inflate": -0.1, "uv": [36, 26], "mirror": true}, {"origin": [-3, 23, -19.5], "size": [6, 2, 4], "inflate": -0.1, "uv": [0, 58], "mirror": true}, {"origin": [-2.5, 24.2, -15.3], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [1.5, 24.2, -15.3], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-4.5, 24.2, -5.3], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [3.5, 24.2, -5.3], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-3.5, 24.2, -10.3], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [2.5, 24.2, -10.3], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-2.5, 24.2, -17.7], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [1.5, 24.2, -17.7], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-4.5, 24.2, -7.7], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [3.5, 24.2, -7.7], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-3.5, 24.2, -12.7], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [2.5, 24.2, -12.7], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-2.5, 24.2, -16.5], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [1.5, 24.2, -16.5], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-4.5, 24.2, -6.5], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [3.5, 24.2, -6.5], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-3.5, 24.2, -11.5], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [2.5, 24.2, -11.5], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-2.5, 24.2, -18.9], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [1.5, 24.2, -18.9], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-0.5, 24.2, -18.9], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [0.5, 24.2, -18.9], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-1.5, 24.2, -18.9], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-4.5, 24.2, -8.9], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [3.5, 24.2, -8.9], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-3.5, 24.2, -13.9], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [2.5, 24.2, -13.9], "size": [1, 2, 1], "inflate": -0.2, "uv": [0, 0], "mirror": true}, {"origin": [-2.5, 25.4, -15.3], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [1.5, 25.4, -15.3], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-4.5, 25.4, -5.3], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [3.5, 25.4, -5.3], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-3.5, 25.4, -10.3], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [2.5, 25.4, -10.3], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-2.5, 25.4, -17.7], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [1.5, 25.4, -17.7], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-4.5, 25.4, -7.7], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [3.5, 25.4, -7.7], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-3.5, 25.4, -12.7], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [2.5, 25.4, -12.7], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-2.5, 25.4, -16.5], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [1.5, 25.4, -16.5], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-4.5, 25.4, -6.5], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [3.5, 25.4, -6.5], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-3.5, 25.4, -11.5], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [2.5, 25.4, -11.5], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-2.5, 25.4, -18.9], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [1.5, 25.4, -18.9], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-0.5, 25.4, -18.9], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [0.5, 25.4, -18.9], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-1.5, 25.4, -18.9], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-4.5, 25.4, -8.9], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [3.5, 25.4, -8.9], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [-3.5, 25.4, -13.9], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}, {"origin": [2.5, 25.4, -13.9], "size": [1, 2, 1], "inflate": -0.35, "uv": [10, 0], "mirror": true}]}]}]}