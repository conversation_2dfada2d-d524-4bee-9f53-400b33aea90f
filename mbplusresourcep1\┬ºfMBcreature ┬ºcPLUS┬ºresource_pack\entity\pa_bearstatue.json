{"format_version": "1.8.0", "minecraft:client_entity": {"description": {"identifier": "pa:bears<PERSON>ue", "min_engine_version": "1.8.0", "materials": {"default": "s<PERSON><PERSON>"}, "textures": {"undyed": "textures/entity/pamobile/pa_bearstatue", "white": "textures/entity/pamobile/pa_bearstatue", "orange": "textures/entity/pamobile/pa_bearstatue", "magenta": "textures/entity/pamobile/pa_bearstatue", "light_blue": "textures/entity/pamobile/pa_bearstatue", "yellow": "textures/entity/pamobile/pa_bearstatue", "lime": "textures/entity/pamobile/pa_bearstatue", "pink": "textures/entity/pamobile/pa_bearstatue", "gray": "textures/entity/pamobile/pa_bearstatue", "silver": "textures/entity/pamobile/pa_bearstatue", "cyan": "textures/entity/pamobile/pa_bearstatue", "purple": "textures/entity/pamobile/pa_bearstatue", "blue": "textures/entity/pamobile/pa_bearstatue", "brown": "textures/entity/pamobile/pa_bearstatue", "green": "textures/entity/pamobile/pa_bearstatue", "red": "textures/entity/pamobile/pa_bearstatue", "black": "textures/entity/pamobile/pa_bearstatue"}, "geometry": {"default": "geometry.pa_bearstatue"}, "spawn_egg": {"texture": "pa:bears<PERSON>ue", "texture_index": 0}, "scripts": {"pre_animation": ["variable.Shulker.LidPositionFactor = 180 * (0.5 + variable.Shulker.PeekAmount);", "variable.Shulker.LidRotationFactor = -1 + Math.sin(180 * (0.5 + variable.Shulker.PeekAmount));", "variable.Shulker.UpFacing = variable.Shulker.FacingDirection == 1;", "variable.Shulker.NorthFacing = variable.Shulker.FacingDirection == 2;", "variable.Shulker.SouthFacing = variable.Shulker.FacingDirection == 3;", "variable.Shulker.WestFacing = variable.Shulker.FacingDirection == 4;", "variable.Shulker.EastFacing = variable.Shulker.FacingDirection == 5;", "variable.Shulker.XPreRotation = variable.Shulker.UpFacing * 180 + variable.Shulker.NorthFacing * 90 - variable.Shulker.SouthFacing * 90;", "variable.Shulker.ZPreRotation = variable.Shulker.NorthFacing * 180 + variable.Shulker.WestFacing * 90 - variable.Shulker.EastFacing * 90;", "variable.Shulker.XOffset = -variable.Shulker.WestFacing * 7.99 + variable.Shulker.EastFacing * 7.99;", "variable.Shulker.YOffset = variable.Shulker.UpFacing * 16 + variable.Shulker.NorthFacing * 7.99 + variable.Shulker.SouthFacing * 7.99 + variable.Shulker.WestFacing * 7.99 + variable.Shulker.EastFacing * 7.99;", "variable.Shulker.ZOffset = variable.Shulker.NorthFacing * 7.99 - variable.Shulker.SouthFacing * 7.99;"]}, "animations": {"facing": "animation.shulker.facing", "move": "animation.shulker.move", "look_at_target": "animation.common.look_at_target"}, "animation_controllers": [{"facing": "controller.animation.shulker.facing"}, {"move": "controller.animation.shulker.move"}], "render_controllers": ["controller.render.shulker"]}}}