{"format_version": "1.8.0", "animations": {"animation.bear_v2.idle": {"loop": true, "animation_length": 3, "bones": {"waist": {"rotation": {"0.0": [0, 0, 0], "1.5": [-2.5, 0, 0], "3.0": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "1.5": [0, -0.6, 0], "3.0": [0, 0, 0]}}, "leg2": {"rotation": {"0.0": [0, 0, 0], "1.5": [2.5, 0, 0], "3.0": [0, 0, 0]}}, "leg3": {"rotation": {"0.0": [0, 0, 0], "1.5": [2.5, 0, 0], "3.0": [0, 0, 0]}}}}, "animation.bear_v2.walk": {"loop": true, "animation_length": 0.6875, "bones": {"waist": {"rotation": {"0.0": [0, 0, 0], "0.125": [-2, 0, 0], "0.25": [0, 0, 0], "0.375": [-2, 0, 0], "0.5": [0, 0, 0]}}, "leg0": {"rotation": {"0.0": [25, 0, 0], "0.0625": [10, 0, 0], "0.1875": [-10, 0, 0], "0.25": [-32.5, 0, 0], "0.3125": [-27.5, 0, 0], "0.4375": [-40, 0, 0], "0.5": [-5, 0, 0], "0.5625": [10, 0, 0], "0.6875": [25, 0, 0]}}, "leg0_0": {"rotation": {"0.0": [17.5, 0, 0], "0.0625": [72.5, 0, 0], "0.1875": [47.5, 0, 0], "0.25": [32.5, 0, 0], "0.3125": [0, 0, 0], "0.4375": [40, 0, 0], "0.5": [5, 0, 0], "0.5625": [22.5, 0, 0], "0.6875": [17.5, 0, 0]}, "position": {"0.0": [0, 0.6, 0], "0.0625": [0, 1.6, -1.3], "0.1875": [0, 1.5, -0.6], "0.25": [0, 1, -0.2], "0.3125": [0, 0, 0], "0.4375": [0, 1.2, -0.2], "0.5": [0, 0.2, 0], "0.5625": [0, 0.9, -0.1], "0.6875": [0, 0.6, 0]}}, "leg1": {"rotation": {"0.0": [-27.5, 0, 0], "0.0625": [-40, 0, 0], "0.1875": [-5, 0, 0], "0.25": [10, 0, 0], "0.3125": [25, 0, 0], "0.4375": [10, 0, 0], "0.5": [-10, 0, 0], "0.5625": [-32.5, 0, 0], "0.6875": [-27.5, 0, 0]}}, "leg0_1": {"rotation": {"0.0": [0, 0, 0], "0.0625": [40, 0, 0], "0.1875": [5, 0, 0], "0.25": [22.5, 0, 0], "0.3125": [17.5, 0, 0], "0.4375": [72.5, 0, 0], "0.5": [47.5, 0, 0], "0.5625": [32.5, 0, 0], "0.6875": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0625": [0, 1.2, -0.2], "0.1875": [0, 0.2, 0], "0.25": [0, 0.9, -0.1], "0.3125": [0, 0.6, 0], "0.4375": [0, 1.6, -1.3], "0.5": [0, 1.5, -0.6], "0.5625": [0, 1, -0.2], "0.6875": [0, 0, 0]}}, "leg2": {"rotation": {"0.0": [-27.5, 0, 0], "0.0625": [-40, 0, 0], "0.1875": [-5, 0, 0], "0.25": [10, 0, 0], "0.3125": [25, 0, 0], "0.4375": [10, 0, 0], "0.5": [-10, 0, 0], "0.5625": [-32.5, 0, 0], "0.6875": [-27.5, 0, 0]}}, "leg0_2": {"rotation": {"0.0": [0, 0, 0], "0.0625": [40, 0, 0], "0.1875": [5, 0, 0], "0.25": [22.5, 0, 0], "0.3125": [17.5, 0, 0], "0.4375": [72.5, 0, 0], "0.5": [47.5, 0, 0], "0.5625": [32.5, 0, 0], "0.6875": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0625": [0, 1.2, -0.2], "0.1875": [0, 0.2, 0], "0.25": [0, 0.9, -0.1], "0.3125": [0, 0.6, 0], "0.4375": [0, 1.6, -1.3], "0.5": [0, 1.5, -0.6], "0.5625": [0, 1, -0.2], "0.6875": [0, 0, 0]}}, "leg3": {"rotation": {"0.0": [25, 0, 0], "0.0625": [10, 0, 0], "0.1875": [-10, 0, 0], "0.25": [-32.5, 0, 0], "0.3125": [-27.5, 0, 0], "0.4375": [-40, 0, 0], "0.5": [-5, 0, 0], "0.5625": [10, 0, 0], "0.6875": [25, 0, 0]}}, "leg0_3": {"rotation": {"0.0": [17.5, 0, 0], "0.0625": [72.5, 0, 0], "0.1875": [47.5, 0, 0], "0.25": [32.5, 0, 0], "0.3125": [0, 0, 0], "0.4375": [40, 0, 0], "0.5": [5, 0, 0], "0.5625": [22.5, 0, 0], "0.6875": [17.5, 0, 0]}, "position": {"0.0": [0, 0.6, 0], "0.0625": [0, 1.6, -1.3], "0.1875": [0, 1.5, -0.6], "0.25": [0, 1, -0.2], "0.3125": [0, 0, 0], "0.4375": [0, 1.2, -0.2], "0.5": [0, 0.2, 0], "0.5625": [0, 0.9, -0.1], "0.6875": [0, 0.6, 0]}}, "bear": {"position": {"0.0": [0, -0.6, 0], "0.0625": [0, -2.4, 0], "0.1875": [0, -0.3, 0], "0.25": [0, -0.1, 0], "0.3125": [0, -0.6, 0], "0.4375": [0, -2.4, 0], "0.5": [0, -0.3, 0], "0.5625": [0, -0.1, 0], "0.6875": [0, -0.6, 0]}}}}, "animation.bear_v2.attack": {"animation_length": 0.54167, "bones": {"waist": {"rotation": {"0.0": [0, 0, 0], "0.1667": [-45, 0, 0], "0.3333": [10, 0, 0], "0.5417": [0, 0, 0]}}, "leg0": {"rotation": {"0.0": [0, 0, 0], "0.1667": [-17.5, 0, 0], "0.5417": [0, 0, 0]}}, "leg0_0": {"rotation": {"0.0": [0, 0, 0], "0.1667": [17.5, 0, 0], "0.5417": [0, 0, 0]}}, "leg1": {"rotation": {"0.0": [0, 0, 0], "0.1667": [-15, 0, 0], "0.5417": [0, 0, 0]}}, "leg0_1": {"rotation": {"0.0": [0, 0, 0], "0.1667": [15, 0, 0], "0.5417": [0, 0, 0]}}, "leg2": {"rotation": {"0.0": [0, 0, 0], "0.1667": [-45, 0, -15], "0.3333": [-60, 0, 0], "0.5417": [0, 0, 0]}}, "leg0_2": {"rotation": {"0.0": [0, 0, 0], "0.1667": [0, 0, 0], "0.3333": [42.5, 0, 0], "0.5417": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.1667": [0, 0, 0], "0.3333": [0, 0.6, -0.3], "0.5417": [0, 0, 0]}}, "leg3": {"rotation": {"0.0": [0, 0, 0], "0.1667": [-45, 0, 15], "0.3333": [30, 0, 0], "0.5417": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.3333": [0, -0.4, 0], "0.5417": [0, 0, 0]}}, "leg0_3": {"rotation": {"0.0": [0, 0, 0], "0.1667": [0, 0, 0], "0.3333": [-42.5, 0, 0], "0.5417": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.1667": [0, 0, 0], "0.3333": [0, 1.1, 0], "0.5417": [0, 0, 0]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.1667": [25, 0, 0], "0.3333": [0, 0, 0], "0.5417": [0, 0, 0]}}, "boca": {"rotation": {"0.0": [0, 0, 0], "0.1667": [32.5, 0, 0], "0.3333": [32.5, 0, 0], "0.5417": [0, 0, 0]}}, "cabeza": {"rotation": {"0.0": [0, 0, 0], "0.2083": [0, 0, 0], "0.3333": [0, 0, 35], "0.5417": [0, 0, 0]}}, "bear": {"position": {"0.0": [0, 0, 0], "0.1667": [0, 0.2, 0], "0.5417": [0, 0, 0]}}}}, "animation.into_morph.on": {"animation_length": 3, "bones": {"into_morph": {"scale": {"0.0": [0, 0, 0], "3.0": [0, 0, 0]}}}}, "animation.bear_v2.two_feet_mode": {"loop": true, "bones": {"bear": {"position": [0, 0, -8]}, "waist": {"rotation": [-87.5, 0, 0]}, "leg2": {"rotation": [82.5, 0, 0], "position": [4, 0, 0]}, "leg0_2": {"rotation": [-27.5, 0, 0], "position": [0, 1, 0]}, "leg3": {"rotation": [82.5, 0, 0], "position": [-4, 0, 0]}, "leg0_3": {"rotation": [-27.5, 0, 0], "position": [0, 1, 0]}, "pat3": {"rotation": [160, 0, 0], "position": [0, -1.3, -2]}, "pat2": {"rotation": [160, 0, 0], "position": [0, -1.3, -2]}, "cabeza": {"rotation": [42.5, 0, 0]}, "head": {"rotation": [40, 0, 0]}}}, "animation.bear_v2.new": {"loop": true, "bones": {"leg1": {"position": [-5, 0, 0]}, "leg0": {"position": [5, 0, 0]}, "waist": {"position": [0, 8, 0]}, "head": {"position": [0, 0, -9]}, "cabeza": {"position": [0, 0, -10]}, "leg2": {"position": [9, 0, 0]}, "leg3": {"position": [-9, 0, 0]}}}, "animation.bear_v2.ground": {"loop": true, "bones": {"leg1": {"rotation": [37.5, 0, 0]}, "leg0": {"rotation": [37.5, 0, 0]}, "leg2": {"rotation": [-52.5, 0, 0]}, "leg3": {"rotation": [-52.5, 0, 0]}}}}}