{"format_version": "1.21.60", "minecraft:crafting_items_catalog": {"categories": [{"category_name": "spawn_eggs", "groups": [{"group_identifier": {"icon": "worldanimals:african_elephant_spawn_egg", "name": "worldanimals:land_animals"}, "items": ["worldanimals:african_elephant_spawn_egg", "worldanimals:asian_elephant_spawn_egg", "worldanimals:mammoth_spawn_egg", "worldanimals:bear_spawn_egg", "worldanimals:black_bear_spawn_egg", "worldanimals:buffalo_spawn_egg", "worldanimals:caracal_spawn_egg", "worldanimals:chimpanzee_spawn_egg", "worldanimals:cougar_spawn_egg", "worldanimals:deer_spawn_egg", "worldanimals:erizo_spawn_egg", "worldanimals:giraffe_spawn_egg", "worldanimals:gorilla_spawn_egg", "worldanimals:hippopotamus_spawn_egg", "worldanimals:hyenas_spawn_egg", "worldanimals:hyenas_2_spawn_egg", "worldanimals:kangaroo_spawn_egg", "worldanimals:leopard_spawn_egg", "worldanimals:lion_spawn_egg", "worldanimals:white_lion_spawn_egg", "worldanimals:panther_spawn_egg", "worldanimals:raccoon_spawn_egg", "worldanimals:red_panda_spawn_egg", "worldanimals:rhinoceros_spawn_egg", "worldanimals:snow_leopard_spawn_egg", "worldanimals:squirrel_spawn_egg", "worldanimals:tiger_spawn_egg", "worldanimals:white_tiger_spawn_egg", "worldanimals:wild_boar_spawn_egg", "worldanimals:zebra_spawn_egg"]}, {"group_identifier": {"icon": "worldanimals:dove_spawn_egg", "name": "worldanimals:birds"}, "items": ["worldanimals:dove_spawn_egg", "worldanimals:duck_spawn_egg", "worldanimals:eagle_spawn_egg", "worldanimals:flamingo_spawn_egg", "worldanimals:kiwi_spawn_egg", "worldanimals:pelican_spawn_egg", "worldanimals:seagull_spawn_egg", "worldanimals:stork_spawn_egg", "worldanimals:tucan_spawn_egg", "worldanimals:turkey_spawn_egg", "worldanimals:vulture_spawn_egg", "worldanimals:cyanocitta_cristata_spawn_egg"]}, {"group_identifier": {"icon": "worldanimals:penguin_spawn_egg", "name": "worldanimals:penguins"}, "items": ["worldanimals:penguin_spawn_egg", "worldanimals:blue_penguin_spawn_egg", "worldanimals:emperor_penguin_spawn_egg", "worldanimals:penguin_african_spawn_egg"]}, {"group_identifier": {"icon": "worldanimals:shark_spawn_egg", "name": "worldanimals:marine_life"}, "items": ["worldanimals:ballena_spawn_egg", "worldanimals:clam_spawn_egg", "worldanimals:crab_spawn_egg", "worldanimals:hammerhead_shark_spawn_egg", "worldanimals:jellyfish_wa_spawn_egg", "worldanimals:lantern_fish_spawn_egg", "worldanimals:orca_spawn_egg", "worldanimals:pink_dolphin_spawn_egg", "worldanimals:seal_spawn_egg", "worldanimals:shark_spawn_egg", "worldanimals:shrimp_spawn_egg", "worldanimals:stingray_spawn_egg", "worldanimals:swordfish_spawn_egg", "worldanimals:tiger_shark_spawn_egg", "worldanimals:white_shark_spawn_egg"]}, {"group_identifier": {"icon": "worldanimals:crocodile_spawn_egg", "name": "worldanimals:reptiles"}, "items": ["worldanimals:crocodile_spawn_egg", "worldanimals:iguana_spawn_egg", "worldanimals:komodo_dragon_spawn_egg", "worldanimals:land_turtle_spawn_egg", "worldanimals:snake_spawn_egg", "worldanimals:snake_coral_spawn_egg", "worldanimals:snake_scarlet_spawn_egg"]}, {"group_identifier": {"icon": "worldanimals:ant_spawn_egg", "name": "worldanimals:small_creatures"}, "items": ["worldanimals:ant_spawn_egg", "worldanimals:butterfly_spawn_egg", "worldanimals:lucien<PERSON>_spawn_egg", "worldanimals:rat_spawn_egg", "worldanimals:snail_spawn_egg"]}, {"group_identifier": {"icon": "worldanimals:village_wild_spawn_egg", "name": "worldanimals:special"}, "items": ["worldanimals:village_ice_spawn_egg", "worldanimals:village_wild_spawn_egg", "worldanimals:orni<PERSON><PERSON><PERSON>_original_spawn_egg", "worldanimals:bag_items_spawn_egg"]}, {"group_identifier": {"icon": "worldanimals:ostrich_egg_spawn_egg", "name": "worldanimals:eggs"}, "items": ["worldanimals:african_penguin_egg_spawn_egg", "worldanimals:blue_penguin_egg_spawn_egg", "worldanimals:duck_egg_spawn_egg", "worldanimals:emperor_penguin_egg_spawn_egg", "worldanimals:ostrich_egg_spawn_egg", "worldanimals:real_penguin_egg_spawn_egg", "worldanimals:turkey_egg_spawn_egg"]}]}]}}