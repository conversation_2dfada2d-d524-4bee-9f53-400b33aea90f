{"format_version": "1.8.0", "minecraft:entity": {"description": {"identifier": "pa:liger", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"minecraft:cow_baby": {"minecraft:is_baby": {}, "minecraft:scale": {"value": 0.5}, "minecraft:ageable": {"duration": 1200, "feedItems": "wheat", "grow_up": {"event": "minecraft:ageable_grow_up", "target": "self"}}, "minecraft:behavior.follow_parent": {"priority": 6, "speed_multiplier": 1.1}}, "minecraft:cow_adult": {"minecraft:behavior.breed": {"priority": 3, "speed_multiplier": 1.0}, "minecraft:breedable": {"requireTame": false, "breedItems": "wheat", "breedsWith": {"mateType": "minecraft:cow", "babyType": "minecraft:cow", "breed_event": {"event": "minecraft:entity_born", "target": "baby"}}}, "minecraft:interact": [{"on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_equipment", "domain": "hand", "subject": "other", "value": "bucket:0"}]}}, "use_item": true, "transform_to_item": "bucket:1", "play_sounds": "milk", "interact_text": "action.interact.milk"}]}}, "components": {"minecraft:type_family": {"family": ["ocelot", "tiger"]}, "minecraft:breathable": {"totalSupply": 15, "suffocateTime": 0}, "minecraft:navigation.walk": {"can_path_over_water": true, "avoid_water": true, "avoid_damage_blocks": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:collision_box": {"height": 1.3, "width": 0.9}, "minecraft:health": {"value": 51, "max": 51}, "minecraft:movement": {"value": 0.55}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.mount_pathing": {"priority": 2, "speed_multiplier": 1.5, "target_dist": 0.0, "track_target": true}, "minecraft:behavior.breed": {"priority": 3, "speed_multiplier": 1.0}, "minecraft:behavior.tempt": {"priority": 4, "speed_multiplier": 1.25, "items": ["wheat"]}, "minecraft:behavior.follow_parent": {"priority": 5, "speed_multiplier": 1.1}, "minecraft:behavior.random_stroll": {"priority": 6, "speed_multiplier": 0.8}, "minecraft:behavior.look_at_player": {"priority": 7, "look_distance": 6.0, "probability": 0.02}, "minecraft:behavior.random_look_around": {"priority": 9}, "minecraft:leashable": {"soft_distance": 4.0, "hard_distance": 6.0, "max_distance": 10.0}, "minecraft:balloonable": {}, "minecraft:physics": {}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 10 : 0"}, "minecraft:scale": {"value": 1}, "minecraft:attack": {"damage": 8}, "minecraft:behavior.melee_attack": {"priority": 2, "speed_multiplier": 1.1, "target_dist": 0.0, "max_dist": 3, "random_stop_interval": 100, "track_target": false, "reach_multiplier": 1.4}, "minecraft:behavior.nearest_attackable_target": {"priority": 3, "within_radius": 99999999, "must_reach": false, "entity_types": [{"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "cow"}, {"test": "is_family", "subject": "other", "value": "illager"}, {"test": "is_family", "subject": "other", "value": "lion"}, {"test": "is_family", "subject": "other", "value": "pig"}, {"test": "is_family", "subject": "other", "value": "pillager"}, {"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_family", "subject": "other", "value": "polarbear"}, {"test": "is_family", "subject": "other", "value": "undead"}, {"test": "is_family", "subject": "other", "value": "vex"}, {"test": "is_family", "subject": "other", "value": "villager"}, {"test": "is_family", "subject": "other", "value": "vindicator"}, {"test": "is_family", "subject": "other", "value": "wolf"}, {"test": "is_family", "subject": "other", "value": "zombie_pigman"}, {"test": "is_family", "subject": "other", "value": "zombie_villager"}]}, "max_dist": 51, "must_see": false}], "must_see": false, "speed_multiplier": 1.0}, "minecraft:behavior.hurt_by_target": {"priority": 3}, "minecraft:behavior.owner_hurt_by_target": {"priority": 1}, "minecraft:behavior.owner_hurt_target": {"priority": 2}, "minecraft:behavior.leap_at_target": {"priority": 4, "yd": 0.6, "must_be_on_ground": false, "target_dist": 0.4, "speed_multiplier": 1.0}, "minecraft:underwater_movement": {"value": 0.02}, "minecraft:tameable": {"probability": 0.33, "tameItems": ["beef", "chicken", "cooked_chicken", "cooked_porkchop", "cooked_salmon", "mushroom_stew", "muttonraw"], "tame_event": {"event": "minecraft:on_tame", "target": "self"}}, "minecraft:input_ground_controlled": {}, "minecraft:is_saddled": {}, "minecraft:behavior.follow_owner": {"start_distance": 10.0, "speed_multiplier": 1.0, "stop_distance": 2.0, "priority": 6}, "minecraft:on_death": {"event": "on:death", "target": "self"}, "minecraft:on_hurt": {"event": "on:hurt", "target": "self"}, "minecraft:on_hurt_by_player": {"event": "on:hurt_by_player", "target": "self"}, "minecraft:on_ignite": {"event": "on:ignite", "target": "self"}, "minecraft:on_target_acquired": {"event": "on:target_acquired", "target": "self"}, "minecraft:on_target_escape": {"event": "on:target_escape", "target": "self"}, "minecraft:on_wake_with_owner": {"event": "on:wake_with_owner", "target": "self"}, "minecraft:knockback_resistance": {"value": 3.0, "max": 3.0}, "minecraft:break_blocks": {"breakable_blocks": ["azalea", "azalea_leaves", "flowering_azalea", "azalea_leaves_flowered", "small_dripleaf", "double_plant", "leaves", "leaves2", "seagrass", "tallgrass"]}, "minecraft:annotation.break_door": {"break_time": 5.0, "min_difficulty": "hard"}, "minecraft:loot": {"table": "loot_tables/entities/pa_liger.json"}}, "events": {"minecraft:entity_spawned": {"randomize": [{"weight": 95, "add": {"component_groups": ["minecraft:cow_adult"]}}, {"weight": 5, "add": {"component_groups": ["minecraft:cow_baby"]}}]}, "minecraft:entity_born": {"add": {"component_groups": ["minecraft:cow_baby"]}}, "minecraft:entity_transformed": {"remove": {}, "add": {"component_groups": ["minecraft:cow_adult"]}}, "minecraft:ageable_grow_up": {"remove": {"component_groups": ["minecraft:cow_baby"]}, "add": {"component_groups": ["minecraft:cow_adult"]}}, "on:death": {"run_command": {"command": []}}, "on:hurt": {"run_command": {"command": []}}, "on:hurt_by_player": {"run_command": {"command": []}}, "on:ignite": {"run_command": {"command": []}}, "on:target_acquired": {"run_command": {"command": []}}, "on:target_escape": {"run_command": {"command": []}}, "on:wake_with_owner": {"run_command": {"command": []}}}}}