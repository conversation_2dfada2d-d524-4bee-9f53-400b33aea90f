{"format_version": "1.16.0", "minecraft:entity": {"description": {"identifier": "worldanimals:snake_coral", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"minecraft:spider_neutral": {"minecraft:environment_sensor": {"triggers": {"filters": {"test": "is_brightness", "operator": "<", "value": 0.49}, "event": "minecraft:become_hostile"}}, "minecraft:on_target_acquired": {"event": "minecraft:become_angry"}}, "minecraft:spider_hostile": {"minecraft:environment_sensor": {"triggers": {"filters": {"test": "is_brightness", "operator": ">", "value": 0.49}, "event": "minecraft:become_neutral"}}, "minecraft:behavior.nearest_attackable_target": {"priority": 2, "must_see": true, "attack_interval": 5, "entity_types": [{"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_family", "subject": "other", "value": "snowgolem"}, {"test": "is_family", "subject": "other", "value": "irongolem"}]}, "max_dist": 16}]}, "minecraft:behavior.leap_at_target": {"priority": 4, "yd": 0.4, "must_be_on_ground": false}, "minecraft:behavior.melee_attack": {"priority": 3, "track_target": true, "random_stop_interval": 100, "reach_multiplier": 0.8}}, "minecraft:spider_angry": {"minecraft:angry": {"duration": 10, "duration_delta": 3, "calm_event": {"event": "minecraft:on_calm", "target": "self"}}, "minecraft:behavior.nearest_attackable_target": {"priority": 2, "must_see": true, "attack_interval": 10, "entity_types": [{"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_family", "subject": "other", "value": "snowgolem"}, {"test": "is_family", "subject": "other", "value": "irongolem"}]}, "max_dist": 16}]}, "minecraft:behavior.leap_at_target": {"priority": 4, "yd": 0.4, "must_be_on_ground": false}, "minecraft:behavior.melee_attack": {"priority": 3, "track_target": true, "reach_multiplier": 1.4}}, "minecraft:spider_poison_easy": {"minecraft:attack": {"damage": 6, "effect_name": "poison", "effect_duration": 0}}, "minecraft:spider_poison_normal": {"minecraft:attack": {"damage": 6, "effect_name": "poison", "effect_duration": 7}}, "minecraft:spider_poison_hard": {"minecraft:attack": {"damage": 6, "effect_name": "poison", "effect_duration": 15}}}, "components": {"minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 5 : 0"}, "minecraft:type_family": {"family": ["<PERSON><PERSON>er", "monster", "arthropod", "mob"]}, "minecraft:breathable": {"total_supply": 15, "suffocate_time": 0}, "minecraft:nameable": {}, "minecraft:loot": {"table": "loot_tables/entities/reptils.json"}, "minecraft:collision_box": {"width": 1.5, "height": 0.3}, "minecraft:health": {"value": 25, "max": 25}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:movement": {"value": 0.3}, "minecraft:underwater_movement": {"value": 0.55}, "minecraft:navigation.generic": {"is_amphibious": true, "can_path_over_water": true, "can_swim": true, "can_walk": true, "can_breach": false, "can_jump": false}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:behavior.float": {"priority": 1}, "minecraft:scale": {"value": 0.5}, "minecraft:behavior.mount_pathing": {"priority": 5, "speed_multiplier": 1.25, "target_dist": 0.0, "track_target": true}, "minecraft:behavior.random_stroll": {"priority": 6, "speed_multiplier": 0.8}, "minecraft:behavior.look_at_player": {"priority": 7, "look_distance": 6.0, "probability": 0.02}, "minecraft:behavior.random_look_around": {"priority": 7}, "minecraft:behavior.hurt_by_target": {"priority": 1}, "minecraft:rideable": {"seat_count": 1, "family_types": ["zombie"], "seats": {"position": [0.0, 0.3, 0.0]}}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}}, "events": {"minecraft:entity_spawned": {"randomize": [{"weight": 1, "add": {"component_groups": ["minecraft:spider_angry"]}}]}, "minecraft:become_hostile": {"sequence": [{"remove": {"component_groups": ["minecraft:spider_neutral"]}, "add": {"component_groups": ["minecraft:spider_hostile"]}}, {"filters": {"test": "is_difficulty", "value": "easy"}, "remove": {"component_groups": ["minecraft:spider_poison_hard", "minecraft:spider_poison_normal"]}, "add": {"component_groups": ["minecraft:spider_poison_easy"]}}, {"filters": {"test": "is_difficulty", "value": "normal"}, "remove": {"component_groups": ["minecraft:spider_poison_easy", "minecraft:spider_poison_hard"]}, "add": {"component_groups": ["minecraft:spider_poison_normal"]}}, {"filters": {"test": "is_difficulty", "value": "hard"}, "remove": {"component_groups": ["minecraft:spider_poison_easy", "minecraft:spider_poison_normal"]}, "add": {"component_groups": ["minecraft:spider_poison_hard"]}}]}, "minecraft:become_neutral": {"remove": {"component_groups": ["minecraft:spider_hostile"]}, "add": {"component_groups": ["minecraft:spider_neutral"]}}, "minecraft:become_angry": {"sequence": [{"remove": {"component_groups": ["minecraft:spider_neutral"]}, "add": {"component_groups": ["minecraft:spider_angry"]}}, {"filters": {"test": "is_difficulty", "value": "easy"}, "remove": {"component_groups": ["minecraft:spider_poison_hard", "minecraft:spider_poison_normal"]}, "add": {"component_groups": ["minecraft:spider_poison_easy"]}}, {"filters": {"test": "is_difficulty", "value": "normal"}, "remove": {"component_groups": ["minecraft:spider_poison_easy", "minecraft:spider_poison_hard"]}, "add": {"component_groups": ["minecraft:spider_poison_normal"]}}, {"filters": {"test": "is_difficulty", "value": "hard"}, "remove": {"component_groups": ["minecraft:spider_poison_easy", "minecraft:spider_poison_normal"]}, "add": {"component_groups": ["minecraft:spider_poison_hard"]}}]}, "minecraft:on_calm": {"remove": {"component_groups": ["minecraft:spider_angry"]}, "add": {"component_groups": ["minecraft:spider_neutral"]}}}}}