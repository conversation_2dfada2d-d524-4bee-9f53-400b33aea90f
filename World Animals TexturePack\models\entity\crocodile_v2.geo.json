{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.crocodile_v2", "texture_width": 128, "texture_height": 128, "visible_bounds_width": 8, "visible_bounds_height": 2.5, "visible_bounds_offset": [0, 0.75, 0]}, "bones": [{"name": "crocodile", "pivot": [0, 0, 0]}, {"name": "head", "parent": "crocodile", "pivot": [0, 5, -19], "cubes": [{"origin": [-3.5, 6, -25], "size": [7, 2, 6], "uv": [22, 68]}, {"origin": [-3, 7.25, -24.5], "size": [6, 1, 5], "inflate": 0.25, "uv": [72, 19]}, {"origin": [-4, 3, -25], "size": [8, 3, 6], "uv": [60, 1]}]}, {"name": "boca0", "parent": "head", "pivot": [0, 4, -23], "cubes": [{"origin": [2, 3.1, -28], "size": [1, 1, 5], "inflate": 0.15, "uv": [47, 83]}, {"origin": [-3, 2.8, -31], "size": [6, 1, 12], "inflate": 0.2, "uv": [8, 91]}, {"origin": [-3, 2.4, -31], "size": [6, 1, 12], "inflate": 0.1, "uv": [8, 91]}, {"origin": [-3, 3.1, -28], "size": [1, 1, 5], "inflate": 0.15, "uv": [34, 6]}, {"origin": [-2, 3, -39], "size": [4, 1, 8], "uv": [0, 70]}, {"origin": [-3, 3.9, -31], "size": [6, 1, 8], "inflate": -0.1, "uv": [100, 0]}, {"origin": [-2, 3.9, -39], "size": [4, 1, 8], "inflate": -0.1, "uv": [90, 9]}]}, {"name": "boca1", "parent": "head", "pivot": [0, 4, -24], "cubes": [{"origin": [-2, 3.9, -41.1], "size": [4, 2, 8], "inflate": 0.1, "uv": [68, 36]}, {"origin": [-2, 6.1, -41.1], "size": [4, 1, 2], "inflate": 0.1, "uv": [0, 7]}, {"origin": [-2, 6.1, -39], "size": [4, 1, 4], "inflate": 0.075, "pivot": [0, 7, -39], "rotation": [-16.75, 0, 0], "uv": [84, 37]}, {"origin": [1, 5.3, -35.2], "size": [1, 1, 3], "inflate": -0.1, "pivot": [0, 5.9, -33], "rotation": [7.5, 0, 0], "uv": [40, 88]}, {"origin": [-2, 5.3, -35.2], "size": [1, 1, 3], "inflate": -0.1, "pivot": [0, 5.9, -33], "rotation": [7.5, 0, 0], "uv": [32, 88]}, {"origin": [-2, 5.3, -31.2], "size": [1, 1, 3], "inflate": -0.1, "pivot": [0, 5.9, -33], "rotation": [7.5, 0, 0], "uv": [34, 76]}, {"origin": [1, 5.3, -31.2], "size": [1, 1, 3], "inflate": -0.1, "pivot": [0, 5.9, -33], "rotation": [7.5, 0, 0], "uv": [38, 47]}, {"origin": [-3, 4.1, -32.9], "size": [6, 2, 9], "inflate": -0.1, "pivot": [0, 5.9, -33], "rotation": [7.5, 0, 0], "uv": [58, 52]}, {"origin": [-3, 0.155, -28.08], "size": [6, 2, 3], "inflate": -0.25, "pivot": [0, 5.9, -33], "rotation": [45, 0, 0], "uv": [79, 56]}, {"origin": [-3, 4, -33], "size": [6, 2, 8], "uv": [50, 63]}, {"origin": [2, 3.5, -30], "size": [1, 1, 5], "inflate": 0.25, "uv": [0, 19]}, {"origin": [-3, 3.5, -30], "size": [1, 1, 5], "inflate": 0.25, "uv": [0, 0]}, {"origin": [-2, 2.9, -41], "size": [4, 1, 8], "inflate": -0.1, "uv": [90, 9]}, {"origin": [-3, 3.05, -33], "size": [6, 1, 3], "inflate": -0.05, "uv": [0, 93]}, {"origin": [-3, 2.45, -30], "size": [6, 1, 5], "inflate": 0.05, "uv": [96, 20]}]}, {"name": "body", "parent": "crocodile", "pivot": [0, 5, -19], "cubes": [{"origin": [-3, 2, -19], "size": [6, 6, 3], "uv": [48, 52]}, {"origin": [-4, 2, -16], "size": [8, 6, 4], "uv": [64, 26]}, {"origin": [-3.5, 7.25, -16], "size": [7, 2, 4], "inflate": -0.025, "uv": [34, 0]}, {"origin": [-2.5, 8, -18.9], "size": [5, 1, 3], "uv": [56, 73]}, {"origin": [-5.5, 1.5, -12], "size": [11, 7, 12], "uv": [0, 19]}, {"origin": [-4.5, 8.5, -12], "size": [9, 1, 12], "uv": [38, 39]}, {"origin": [3.5, 9.25, -11.5], "size": [1, 2, 12], "inflate": -0.25, "uv": [102, 114]}, {"origin": [-5, 9.25, -11.5], "size": [1, 2, 12], "inflate": -0.25, "uv": [102, 114]}, {"origin": [-1, 9, -10.5], "size": [1, 2, 12], "inflate": -0.25, "uv": [102, 114]}]}, {"name": "body0", "parent": "body", "pivot": [0, 5, 0]}, {"name": "body1", "parent": "body0", "pivot": [0, 5, 0], "cubes": [{"origin": [-4.5, 8.5, 0], "size": [9, 1, 12], "uv": [34, 26]}, {"origin": [3.5, 9.25, 0], "size": [1, 2, 12], "inflate": -0.25, "uv": [102, 100]}, {"origin": [-5, 9.25, 0], "size": [1, 2, 12], "inflate": -0.25, "uv": [102, 100]}, {"origin": [-0.75, 9, 1], "size": [1, 2, 12], "inflate": -0.35, "uv": [102, 100]}, {"origin": [-5.5, 1.5, 0], "size": [11, 7, 12], "uv": [0, 0]}]}, {"name": "tail", "parent": "body1", "pivot": [0, 5.5, 11.1], "rotation": [-10, 0, 0], "cubes": [{"origin": [-4.5, 2.5, 11.1], "size": [9, 7, 10], "uv": [0, 38]}, {"origin": [3.5, 9.225, 10.1], "size": [1, 2, 11], "inflate": -0.275, "uv": [78, 115]}, {"origin": [-5, 9.225, 10.1], "size": [1, 2, 11], "inflate": -0.275, "uv": [78, 115]}]}, {"name": "tail2", "parent": "tail", "pivot": [0, 5.5, 20.8], "rotation": [7.5, 0, 0], "cubes": [{"origin": [-3.5, 2.5, 20.8], "size": [7, 7, 12], "uv": [34, 7]}, {"origin": [2.5, 9.25, 20.8], "size": [1, 2, 12], "inflate": -0.25, "uv": [102, 86]}, {"origin": [-4, 9.25, 20.8], "size": [1, 2, 12], "inflate": -0.25, "uv": [102, 86]}, {"origin": [-0.75, 9, 20.8], "size": [1, 2, 12], "inflate": -0.25, "uv": [102, 86]}]}, {"name": "tail3", "parent": "tail2", "pivot": [0, 6.3, 32.4], "rotation": [-2.5, 0, 0], "cubes": [{"origin": [-2.5, 3.3, 32.4], "size": [5, 6, 10], "uv": [28, 52]}]}, {"name": "tail4", "parent": "tail3", "pivot": [0, 6.3, 42.4], "rotation": [2.5, 0, 0], "cubes": [{"origin": [-2, 3.8, 42.4], "size": [4, 5, 10], "uv": [0, 55]}]}, {"name": "leg3", "parent": "body0", "pivot": [-5, 7, 8], "cubes": [{"origin": [-8, 3, 6], "size": [5, 4, 4], "pivot": [-5, 5, 8], "rotation": [0, 20, -25], "uv": [79, 48]}, {"origin": [-12.2, 3, 6], "size": [5, 3, 4], "inflate": -0.15, "pivot": [-5, 5, 8], "rotation": [0, 20, -25], "uv": [34, 81]}, {"origin": [-12, 0, 4], "size": [4, 2, 6], "pivot": [-5, 5, 8], "rotation": [0, 20, 0], "uv": [42, 73]}, {"origin": [-9, 0, 2], "size": [1, 2, 3], "inflate": -0.1, "pivot": [-5, 5, 8], "rotation": [0, 20, 0], "uv": [88, 5]}, {"origin": [-9, 0, 0], "size": [1, 2, 3], "inflate": -0.3, "pivot": [-5, 5, 8], "rotation": [0, 20, 0], "uv": [8, 87]}, {"origin": [-12, 0, 2], "size": [1, 2, 3], "inflate": -0.1, "pivot": [-5, 5, 8], "rotation": [0, 20, 0], "uv": [0, 87]}, {"origin": [-12, 0, 0], "size": [1, 2, 3], "inflate": -0.3, "pivot": [-5, 5, 8], "rotation": [0, 20, 0], "uv": [83, 86]}, {"origin": [-10.5, 0, 2], "size": [1, 2, 3], "inflate": -0.1, "pivot": [-5, 5, 8], "rotation": [0, 20, 0], "uv": [75, 86]}, {"origin": [-10.5, 0, 0], "size": [1, 2, 3], "inflate": -0.3, "pivot": [-5, 5, 8], "rotation": [0, 20, 0], "uv": [86, 71]}, {"origin": [-12, 2, 6], "size": [4, 1, 4], "pivot": [-5, 5, 8], "rotation": [0, 20, 0], "uv": [84, 32]}]}, {"name": "leg2", "parent": "body0", "pivot": [5, 5, 8], "cubes": [{"origin": [3, 3, 6], "size": [5, 4, 4], "pivot": [4, 5, 8], "rotation": [0, -20, 25], "uv": [58, 77]}, {"origin": [6.8, 3, 6], "size": [5, 3, 4], "inflate": -0.15, "pivot": [4, 5, 8], "rotation": [0, -20, 25], "uv": [76, 79]}, {"origin": [8, 0, 4], "size": [4, 2, 6], "pivot": [4, 5, 8], "rotation": [0, -20, 0], "uv": [70, 63]}, {"origin": [11, 0, 2], "size": [1, 2, 3], "inflate": -0.1, "pivot": [4, 5, 8], "rotation": [0, -20, 0], "uv": [68, 46]}, {"origin": [11, 0, 0], "size": [1, 2, 3], "inflate": -0.3, "pivot": [4, 5, 8], "rotation": [0, -20, 0], "uv": [42, 68]}, {"origin": [8, 0, 2], "size": [1, 2, 3], "inflate": -0.1, "pivot": [4, 5, 8], "rotation": [0, -20, 0], "uv": [68, 39]}, {"origin": [8, 0, 0], "size": [1, 2, 3], "inflate": -0.3, "pivot": [4, 5, 8], "rotation": [0, -20, 0], "uv": [60, 10]}, {"origin": [9.5, 0, 2], "size": [1, 2, 3], "inflate": -0.1, "pivot": [4, 5, 8], "rotation": [0, -20, 0], "uv": [0, 60]}, {"origin": [9.5, 0, 0], "size": [1, 2, 3], "inflate": -0.3, "pivot": [4, 5, 8], "rotation": [0, -20, 0], "uv": [56, 0]}, {"origin": [8, 2, 6], "size": [4, 1, 4], "pivot": [4, 5, 8], "rotation": [0, -20, 0], "uv": [14, 84]}]}, {"name": "piernas", "parent": "body", "pivot": [0, 2, -5]}, {"name": "leg4", "parent": "piernas", "pivot": [-5, 7, -9], "cubes": [{"origin": [-8, 3, -11], "size": [5, 4, 4], "pivot": [-5, 5, -9], "rotation": [0, 20, -25], "uv": [0, 79]}, {"origin": [-12.2, 3, -11], "size": [5, 3, 4], "inflate": -0.15, "pivot": [-5, 5, -9], "rotation": [0, 20, -25], "uv": [80, 10]}, {"origin": [-12, 0, -13], "size": [4, 2, 6], "pivot": [-5, 5, -9], "rotation": [0, 20, 0], "uv": [72, 71]}, {"origin": [-9, 0, -15], "size": [1, 2, 3], "inflate": -0.1, "pivot": [-5, 5, -9], "rotation": [0, 20, 0], "uv": [27, 86]}, {"origin": [-9, 0, -17], "size": [1, 2, 3], "inflate": -0.3, "pivot": [-5, 5, -9], "rotation": [0, 20, 0], "uv": [67, 85]}, {"origin": [-12, 0, -15], "size": [1, 2, 3], "inflate": -0.1, "pivot": [-5, 5, -9], "rotation": [0, 20, 0], "uv": [59, 85]}, {"origin": [-12, 0, -17], "size": [1, 2, 3], "inflate": -0.3, "pivot": [-5, 5, -9], "rotation": [0, 20, 0], "uv": [84, 63]}, {"origin": [-10.5, 0, -15], "size": [1, 2, 3], "inflate": -0.1, "pivot": [-5, 5, -9], "rotation": [0, 20, 0], "uv": [16, 73]}, {"origin": [-10.5, 0, -17], "size": [1, 2, 3], "inflate": -0.3, "pivot": [-5, 5, -9], "rotation": [0, 20, 0], "uv": [0, 70]}, {"origin": [-12, 2, -11], "size": [4, 1, 4], "pivot": [-5, 5, -9], "rotation": [0, 20, 0], "uv": [84, 25]}]}, {"name": "leg0", "parent": "piernas", "pivot": [5, 5, -9], "cubes": [{"origin": [3, 3, -11], "size": [5, 4, 4], "pivot": [4, 5, -9], "rotation": [0, -20, 25], "uv": [20, 76]}, {"origin": [6.8, 3, -11], "size": [5, 3, 4], "inflate": -0.15, "pivot": [4, 5, -9], "rotation": [0, -20, 25], "uv": [18, 55]}, {"origin": [8, 0, -13], "size": [4, 2, 6], "pivot": [4, 5, -9], "rotation": [0, -20, 0], "uv": [28, 39]}, {"origin": [11, 0, -15], "size": [1, 2, 3], "inflate": -0.1, "pivot": [4, 5, -9], "rotation": [0, -20, 0], "uv": [0, 55]}, {"origin": [11, 0, -17], "size": [1, 2, 3], "inflate": -0.3, "pivot": [4, 5, -9], "rotation": [0, -20, 0], "uv": [0, 43]}, {"origin": [8, 0, -15], "size": [1, 2, 3], "inflate": -0.1, "pivot": [4, 5, -9], "rotation": [0, -20, 0], "uv": [42, 39]}, {"origin": [8, 0, -17], "size": [1, 2, 3], "inflate": -0.3, "pivot": [4, 5, -9], "rotation": [0, -20, 0], "uv": [0, 38]}, {"origin": [9.5, 0, -15], "size": [1, 2, 3], "inflate": -0.1, "pivot": [4, 5, -9], "rotation": [0, -20, 0], "uv": [34, 26]}, {"origin": [9.5, 0, -17], "size": [1, 2, 3], "inflate": -0.3, "pivot": [4, 5, -9], "rotation": [0, -20, 0], "uv": [0, 26]}, {"origin": [8, 2, -11], "size": [4, 1, 4], "pivot": [4, 5, -9], "rotation": [0, -20, 0], "uv": [82, 0]}]}]}]}