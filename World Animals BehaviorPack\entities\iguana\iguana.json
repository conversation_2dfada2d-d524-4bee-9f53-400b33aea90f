{"format_version": "1.16.0", "minecraft:entity": {"description": {"identifier": "worldanimals:iguana", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"minecraft:parrot_red": {"minecraft:variant": {"value": 0}}, "minecraft:parrot_blue": {"minecraft:variant": {"value": 1}}, "minecraft:iguana_baby": {"minecraft:is_baby": {}, "minecraft:scale": {"value": 0.5}, "minecraft:ageable": {"duration": 1200, "feed_items": ["minecraft:carrot", "minecraft:golden_carrot"], "grow_up": {"event": "minecraft:ageable_grow_up", "target": "self"}}}, "minecraft:iguana_adult": {"minecraft:experience_reward": {"on_bred": "<PERSON><PERSON>(1,7)", "on_death": "query.last_hit_by_player ? Math.Random(1,3) : 0"}, "minecraft:loot": {"table": "loot_tables/entities/iguana.json"}, "minecraft:behavior.breed": {"priority": 3, "speed_multiplier": 1.0}, "minecraft:breedable": {"require_tame": true, "require_full_health": true, "breeds_with": {"mate_type": "worldanimals:iguana", "baby_type": "worldanimals:iguana", "breed_event": {"event": "minecraft:entity_born", "target": "baby"}}, "breed_items": ["chicken", "cooked_chicken", "beef", "cooked_beef", "muttonRaw", "muttonCooked", "porkchop", "cooked_porkchop", "rabbit", "cooked_rabbit", "rotten_flesh"]}}, "minecraft:iguana_angry": {"minecraft:angry": {"duration": 25, "broadcast_anger": true, "broadcast_range": 20, "calm_event": {"event": "minecraft:on_calm", "target": "self"}}, "minecraft:on_target_acquired": {}, "minecraft:rideable": {"seat_count": 1, "family_types": ["zombie"], "seats": {"position": [0.0, 0.675, -0.1]}}}, "minecraft:iguana_wild": {"minecraft:behavior.avoid_mob_type": {"priority": 3, "entity_types": [{"filters": {"test": "is_family", "subject": "other", "value": "llama"}, "max_dist": 24, "walk_speed_multiplier": 1.5, "sprint_speed_multiplier": 1.5}], "probability_per_strength": 0.14}, "minecraft:tameable": {"probability": 1.0, "tame_items": "worldanimals:collar", "tame_event": {"event": "minecraft:on_tame", "target": "self"}}, "minecraft:on_target_acquired": {"event": "minecraft:become_angry", "target": "self"}, "minecraft:rideable": {"seat_count": 1, "family_types": ["zombie"], "seats": {"position": [0.0, 0.675, -0.1]}}}, "minecraft:iguana_tame": {"minecraft:is_tamed": {}, "minecraft:health": {"value": 30, "max": 30}, "minecraft:behavior.follow_owner": {"priority": 6, "speed_multiplier": 1.0, "start_distance": 10, "stop_distance": 2}, "minecraft:attack": {"damage": 4}, "minecraft:behavior.breed": {"priority": 2, "speed_multiplier": 1.0}, "minecraft:behavior.owner_hurt_by_target": {"priority": 1}, "minecraft:behavior.owner_hurt_target": {"priority": 2}, "minecraft:behavior.nearest_attackable_target": {"priority": 5, "must_see": true, "entity_types": [{"filters": {"test": "is_family", "subject": "other", "value": "skeleton"}, "max_dist": 16}]}, "minecraft:sittable": {}, "minecraft:is_dyeable": {"interact_text": "action.interact.dye"}, "minecraft:leashable": {"soft_distance": 4.0, "hard_distance": 6.0, "max_distance": 10.0, "on_leash": {"event": "minecraft:on_leash", "target": "self"}, "on_unleash": {"event": "minecraft:on_unleash", "target": "self"}}}}, "components": {"minecraft:nameable": {}, "minecraft:type_family": {"family": ["iguana", "mob"]}, "minecraft:breathable": {"total_supply": 15, "suffocate_time": 0}, "minecraft:collision_box": {"width": 0.6, "height": 0.8}, "minecraft:health": {"value": 8, "max": 8}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:movement": {"value": 0.3}, "minecraft:navigation.walk": {"can_path_over_water": true, "avoid_damage_blocks": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:attack": {"damage": 3}, "minecraft:healable": {"items": [{"item": "minecraft:carrot", "heal_amount": 3}, {"item": "minecraft:golden_carrot", "heal_amount": 8}]}, "minecraft:despawn": {"despawn_from_distance": {}}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.mount_pathing": {"priority": 1, "speed_multiplier": 1.25, "target_dist": 0, "track_target": true}, "minecraft:behavior.stay_while_sitting": {"priority": 3}, "minecraft:behavior.leap_at_target": {"priority": 4, "target_dist": 0.4}, "minecraft:behavior.melee_attack": {"priority": 5}, "minecraft:behavior.random_stroll": {"priority": 8, "speed_multiplier": 1.0}, "minecraft:behavior.look_at_player": {"priority": 6, "target_distance": 6.0, "probability": 0.02}, "minecraft:behavior.beg": {"priority": 9, "look_distance": 8, "look_time": [2, 4], "items": ["bone", "porkchop", "cooked_porkchop", "chicken", "cooked_chicken", "beef", "cooked_beef", "rotten_flesh", "muttonraw", "muttoncooked", "rabbit", "cooked_rabbit"]}, "minecraft:behavior.hurt_by_target": {"priority": 3}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}}, "events": {"minecraft:entity_spawned": {"randomize": [{"weight": 4, "remove": {}, "add": {"component_groups": ["minecraft:iguana_adult", "minecraft:parrot_blue", "minecraft:iguana_wild"]}}, {"weight": 1, "remove": {}, "add": {"component_groups": ["minecraft:iguana_baby", "minecraft:parrot_blue", "minecraft:iguana_wild"]}}, {"weight": 4, "remove": {}, "add": {"component_groups": ["minecraft:iguana_adult", "minecraft:parrot_red", "minecraft:iguana_wild"]}}, {"weight": 1, "remove": {}, "add": {"component_groups": ["minecraft:iguana_baby", "minecraft:parrot_red", "minecraft:iguana_wild"]}}]}, "minecraft:entity_born": {"remove": {}, "add": {"component_groups": ["minecraft:iguana_baby", "minecraft:iguana_tame"]}}, "minecraft:ageable_grow_up": {"remove": {"component_groups": ["minecraft:iguana_baby"]}, "add": {"component_groups": ["minecraft:iguana_adult"]}}, "minecraft:ageable_set_baby": {"remove": {"component_groups": ["minecraft:iguana_adult"]}, "add": {"component_groups": ["minecraft:iguana_baby"]}}, "minecraft:on_tame": {"remove": {"component_groups": ["minecraft:iguana_wild"]}, "add": {"component_groups": ["minecraft:iguana_tame"]}}, "minecraft:become_angry": {"remove": {"component_groups": ["minecraft:iguana_wild"]}, "add": {"component_groups": ["minecraft:iguana_angry"]}}, "minecraft:on_calm": {"remove": {"component_groups": ["minecraft:iguana_angry"]}, "add": {"component_groups": ["minecraft:iguana_wild"]}}}}}