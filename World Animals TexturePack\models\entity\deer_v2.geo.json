{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.deer_v2", "texture_width": 64, "texture_height": 64, "visible_bounds_width": 4, "visible_bounds_height": 4.5, "visible_bounds_offset": [0, 1.75, 0]}, "bones": [{"name": "deer", "pivot": [0, 0, 0]}, {"name": "body", "parent": "deer", "pivot": [0, 10, 0], "cubes": [{"origin": [-4, 10, -8], "size": [8, 8, 8], "uv": [0, 0]}, {"origin": [-3.5, 10, -0.9], "size": [7, 7, 8], "pivot": [0, 13.5, 4], "rotation": [-12.5, 0, 0], "uv": [0, 16]}]}, {"name": "head", "parent": "body", "pivot": [0, 15, -8]}, {"name": "cabeza0", "parent": "head", "pivot": [0, 15, -8], "rotation": [42.5, 0, 0], "cubes": [{"origin": [-2, 18, -9.5], "size": [4, 6, 4], "pivot": [0, 19, -8], "rotation": [-35, 0, 0], "uv": [40, 21]}, {"origin": [-2.5, 11, -10], "size": [5, 8, 5], "uv": [25, 26]}]}, {"name": "cabeza", "parent": "cabeza0", "pivot": [0, 23, -5], "rotation": [-42.5, 0, 0], "cubes": [{"origin": [-2.5, 22.5, -7.5], "size": [5, 5, 5], "uv": [27, 11]}, {"origin": [-1.5, 23, -11.5], "size": [3, 2, 5], "uv": [24, 0]}, {"origin": [-1.5, 24.25, -11], "size": [3, 1, 5], "inflate": -0.025, "pivot": [0, 25.5, -9.5], "rotation": [22.5, 0, 0], "uv": [24, 51]}]}, {"name": "boca", "parent": "cabeza", "pivot": [0, 22.65, -7.85], "cubes": [{"origin": [-1.5, 22.15, -11.35], "size": [3, 1, 5], "inflate": -0.15, "uv": [47, 12]}]}, {"name": "<PERSON><PERSON><PERSON>", "parent": "cabeza", "pivot": [0.1, 27.6, -3.5]}, {"name": "cuerno0", "parent": "<PERSON><PERSON><PERSON>", "pivot": [1.6, 27.3, -3.5], "rotation": [0, 0, 7.5], "cubes": [{"origin": [1.1, 26.45, -4], "size": [1, 1, 1], "inflate": 0.25, "uv": [24, 48]}, {"origin": [1.1, 27.7, -4], "size": [1, 2, 1], "uv": [46, 12]}, {"origin": [1.19284, 29.35, -4], "size": [1, 2, 1], "inflate": -0.025, "pivot": [1.6, 29.7, -3.5], "rotation": [0, 0, 40], "uv": [45, 31]}, {"origin": [1.19284, 29.35, -4], "size": [1, 3, 1], "inflate": -0.3, "pivot": [1.6, 29.7, -3.5], "rotation": [45, 0, -17.5], "uv": [24, 0]}, {"origin": [3.26587, 30.57955, -4.8], "size": [1, 2, 1], "inflate": -0.3, "pivot": [3.59639, 32.0932, -4.3], "rotation": [29.62165, -4.98093, 8.6822], "uv": [9, 43]}, {"origin": [3.29672, 31.59562, -4.8], "size": [1, 2, 1], "inflate": -0.35, "pivot": [3.59639, 32.0932, -4.3], "rotation": [26.56505, 14.47751, -26.56505], "uv": [42, 12]}, {"origin": [5.36587, 30.77955, -3.2], "size": [1, 2, 1], "inflate": -0.25, "pivot": [5.69639, 32.2932, -2.7], "rotation": [-52.07555, 7.9185, 6.12673], "uv": [41, 0]}, {"origin": [5.39672, 31.79562, -3.2], "size": [1, 2, 1], "inflate": -0.3, "pivot": [5.69639, 32.2932, -2.7], "rotation": [-48.458, -23.37058, -19.36497], "uv": [38, 39]}, {"origin": [2.36784, 30.275, -4], "size": [3, 1, 1], "inflate": -0.05, "pivot": [2.6, 30.7, -3.5], "rotation": [0, 0, -7.5], "uv": [0, 21]}, {"origin": [4.82962, 29.1504, -4], "size": [2, 1, 1], "inflate": -0.075, "pivot": [2.6, 30.7, -3.5], "rotation": [0, 0, -32.5], "uv": [22, 22]}, {"origin": [5.97962, 31.6504, -4], "size": [1, 2, 1], "inflate": -0.1, "pivot": [6.47962, 31.6004, -3.5], "rotation": [0, 0, 7.5], "uv": [28, 39]}, {"origin": [6.18122, 33.26834, -4], "size": [1, 1, 1], "inflate": -0.125, "pivot": [6.68122, 33.76834, -3.5], "rotation": [0, 0, -10], "uv": [21, 47]}, {"origin": [6.33122, 33.76834, -4], "size": [1, 1, 1], "inflate": -0.15, "pivot": [6.68122, 33.76834, -3.5], "rotation": [0, 0, -37.5], "uv": [47, 18]}, {"origin": [6.53122, 34.26834, -4], "size": [1, 1, 1], "inflate": -0.175, "pivot": [6.68122, 33.76834, -3.5], "rotation": [0, 0, -52.5], "uv": [47, 15]}, {"origin": [7.03122, 34.46834, -4], "size": [1, 2, 1], "inflate": -0.325, "pivot": [6.68122, 33.76834, -3.5], "rotation": [0, 0, -72.5], "uv": [38, 21]}, {"origin": [6.23122, 33.96834, -4], "size": [1, 2, 1], "inflate": -0.325, "pivot": [6.68122, 33.76834, -3.5], "rotation": [0, 0, -7.5], "uv": [38, 2]}, {"origin": [6.93122, 33.36834, -4], "size": [1, 2, 1], "inflate": -0.325, "pivot": [6.68122, 33.76834, -3.5], "rotation": [0, 0, 65], "uv": [36, 7]}]}, {"name": "cuerno1", "parent": "<PERSON><PERSON><PERSON>", "pivot": [-1.4, 27.3, -3.5], "rotation": [180, 0, 172.5], "cubes": [{"origin": [-1.9, 26.45, -4], "size": [1, 1, 1], "inflate": 0.25, "uv": [45, 34]}, {"origin": [-1.9, 27.7, -4], "size": [1, 2, 1], "uv": [35, 0]}, {"origin": [-1.80716, 29.35, -4], "size": [1, 2, 1], "inflate": -0.025, "pivot": [-1.4, 29.7, -3.5], "rotation": [0, 0, 40], "uv": [34, 21]}, {"origin": [-1.80716, 29.35, -4], "size": [1, 3, 1], "inflate": -0.3, "pivot": [-1.4, 29.7, -3.5], "rotation": [45, 0, -17.5], "uv": [22, 16]}, {"origin": [0.26587, 30.57955, -4.8], "size": [1, 2, 1], "inflate": -0.3, "pivot": [0.59639, 32.0932, -4.3], "rotation": [29.62165, -4.98093, 8.6822], "uv": [14, 34]}, {"origin": [0.29672, 31.59562, -4.8], "size": [1, 2, 1], "inflate": -0.35, "pivot": [0.59639, 32.0932, -4.3], "rotation": [26.56505, 14.47751, -26.56505], "uv": [32, 7]}, {"origin": [2.36587, 30.77955, -3.2], "size": [1, 2, 1], "inflate": -0.25, "pivot": [2.69639, 32.2932, -2.7], "rotation": [-52.07555, 7.9185, 6.12673], "uv": [31, 23]}, {"origin": [2.39672, 31.79562, -3.2], "size": [1, 2, 1], "inflate": -0.3, "pivot": [2.69639, 32.2932, -2.7], "rotation": [-48.458, -23.37058, -19.36497], "uv": [18, 31]}, {"origin": [-0.63216, 30.275, -4], "size": [3, 1, 1], "inflate": -0.05, "pivot": [-0.4, 30.7, -3.5], "rotation": [0, 0, -7.5], "uv": [0, 5]}, {"origin": [1.82962, 29.1504, -4], "size": [2, 1, 1], "inflate": -0.075, "pivot": [-0.4, 30.7, -3.5], "rotation": [0, 0, -32.5], "uv": [22, 20]}, {"origin": [2.97962, 31.6504, -4], "size": [1, 2, 1], "inflate": -0.1, "pivot": [3.47962, 31.6004, -3.5], "rotation": [0, 0, 7.5], "uv": [14, 31]}, {"origin": [3.18122, 33.26834, -4], "size": [1, 1, 1], "inflate": -0.125, "pivot": [3.68122, 33.76834, -3.5], "rotation": [0, 0, -10], "uv": [14, 37]}, {"origin": [3.33122, 33.76834, -4], "size": [1, 1, 1], "inflate": -0.15, "pivot": [3.68122, 33.76834, -3.5], "rotation": [0, 0, -37.5], "uv": [35, 24]}, {"origin": [3.53122, 34.26834, -4], "size": [1, 1, 1], "inflate": -0.175, "pivot": [3.68122, 33.76834, -3.5], "rotation": [0, 0, -52.5], "uv": [21, 33]}, {"origin": [4.03122, 34.46834, -4], "size": [1, 2, 1], "inflate": -0.325, "pivot": [3.68122, 33.76834, -3.5], "rotation": [0, 0, -72.5], "uv": [10, 31]}, {"origin": [3.23122, 33.96834, -4], "size": [1, 2, 1], "inflate": -0.325, "pivot": [3.68122, 33.76834, -3.5], "rotation": [0, 0, -7.5], "uv": [0, 31]}, {"origin": [3.93122, 33.36834, -4], "size": [1, 2, 1], "inflate": -0.325, "pivot": [3.68122, 33.76834, -3.5], "rotation": [0, 0, 65], "uv": [28, 21]}]}, {"name": "oreja0", "parent": "cabeza", "pivot": [2, 26, -3.5], "rotation": [0, 0, 62.5], "cubes": [{"origin": [1, 25, -4], "size": [2, 4, 1], "uv": [0, 16]}]}, {"name": "oreja1", "parent": "cabeza", "pivot": [-2, 26, -3.5], "rotation": [0, 0, -62.5], "cubes": [{"origin": [-3, 25, -4], "size": [2, 4, 1], "uv": [0, 0]}]}, {"name": "tail", "parent": "body", "pivot": [0, 15, 7], "rotation": [-22.5, 0, 0], "cubes": [{"origin": [-2, 15, 6], "size": [4, 4, 2], "uv": [51, 31]}]}, {"name": "leg3", "parent": "deer", "pivot": [2, 12, 4], "cubes": [{"origin": [1, 7.25, 3], "size": [3, 8, 4], "pivot": [2, 10, 6], "rotation": [12.5, 0, 0], "uv": [42, 0]}]}, {"name": "leg33", "parent": "leg3", "pivot": [2, 8, 6], "cubes": [{"origin": [1, -0.25, 4.2], "size": [3, 8, 3], "inflate": -0.25, "uv": [42, 47]}]}, {"name": "leg1", "parent": "deer", "pivot": [3, 12, -5], "cubes": [{"origin": [2, 7.25, -6.5], "size": [3, 8, 4], "pivot": [3, 10, -4], "rotation": [2.5, 0, 0], "uv": [28, 39]}]}, {"name": "leg11", "parent": "leg1", "pivot": [3, 8, -4], "cubes": [{"origin": [2, -0.25, -5.8], "size": [3, 8, 3], "inflate": -0.25, "uv": [12, 47]}]}, {"name": "leg0", "parent": "deer", "pivot": [-4, 12, -5], "cubes": [{"origin": [-5, 7.25, -6.5], "size": [3, 8, 4], "pivot": [-4, 10, -4], "rotation": [2.5, 0, 0], "uv": [14, 35]}]}, {"name": "leg00", "parent": "leg0", "pivot": [-4, 8, -4], "cubes": [{"origin": [-5, -0.25, -5.8], "size": [3, 8, 3], "inflate": -0.25, "uv": [0, 43]}]}, {"name": "leg2", "parent": "deer", "pivot": [-3, 12, 4], "cubes": [{"origin": [-4, 7.25, 3], "size": [3, 8, 4], "pivot": [-3, 10, 6], "rotation": [12.5, 0, 0], "uv": [0, 31]}]}, {"name": "leg22", "parent": "leg2", "pivot": [-3, 8, 6], "cubes": [{"origin": [-4, -0.25, 4.2], "size": [3, 8, 3], "inflate": -0.25, "uv": [42, 36]}]}]}]}