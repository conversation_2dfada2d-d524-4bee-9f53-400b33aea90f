{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.hyenas", "texture_width": 256, "texture_height": 256, "visible_bounds_width": 4, "visible_bounds_height": 4, "visible_bounds_offset": [0, 1, 0]}, "bones": [{"name": "hyena", "pivot": [0, 0, 0]}, {"name": "leg3", "parent": "hyena", "pivot": [4, 12, 10], "cubes": [{"origin": [2.5, 0, 8.25], "size": [3, 8, 3], "uv": [0, 32]}, {"origin": [2.5, 0, 6.25], "size": [3, 2, 2], "uv": [20, 32]}, {"origin": [2, 6, 8], "size": [4, 8, 4], "uv": [46, 30]}]}, {"name": "leg2", "parent": "hyena", "pivot": [-3, 12, 10], "cubes": [{"origin": [-4.5, 0, 8.25], "size": [3, 8, 3], "uv": [0, 32]}, {"origin": [-4.5, 0, 6.25], "size": [3, 2, 2], "uv": [20, 32]}, {"origin": [-5, 6, 8], "size": [4, 8, 4], "uv": [46, 30]}]}, {"name": "leg1", "parent": "hyena", "pivot": [4, 14, -4], "cubes": [{"origin": [1.5, 0, -5.75], "size": [3, 8, 3], "uv": [0, 32]}, {"origin": [1.5, 0, -7.75], "size": [3, 2, 2], "uv": [20, 32]}, {"origin": [1, 8, -6], "size": [4, 8, 4], "uv": [46, 30]}]}, {"name": "leg0", "parent": "hyena", "pivot": [-3, 14, -4], "cubes": [{"origin": [-4.5, 0, -5.75], "size": [3, 8, 3], "uv": [0, 32]}, {"origin": [-4.5, 0, -7.75], "size": [3, 2, 2], "uv": [20, 32]}, {"origin": [-5, 8, -6], "size": [4, 8, 4], "uv": [46, 30]}]}, {"name": "cuerpo", "parent": "hyena", "pivot": [0, 9, -7], "cubes": [{"origin": [-3.5, 9, -7], "size": [7, 8, 8], "inflate": 0.75, "uv": [159, 0]}, {"origin": [-1, 17.8, -8], "size": [1, 6, 10], "uv": [0, 47]}, {"origin": [-1, 16.2, -1.4], "size": [1, 6, 10], "pivot": [0, 0, 7], "rotation": [-10, 0, 0], "uv": [0, 47]}, {"origin": [-3.5, 9, 5], "size": [7, 8, 8], "inflate": 0.75, "pivot": [-0.5, 9, 0], "rotation": [-10, 0, 0], "uv": [159, 0]}, {"origin": [-0.5, 6, 15.25], "size": [2, 6, 2], "pivot": [-2.5, 6, 10.25], "rotation": [20, 0, 0], "uv": [115, 20]}, {"origin": [-0.5, 4.25, 14.85], "size": [2, 4, 2], "inflate": 0.15, "pivot": [-2.5, 7, 9.85], "rotation": [10, 0, 0], "uv": [134, 0]}, {"origin": [-3.5, 9, 0], "size": [7, 8, 8], "inflate": 0.6, "pivot": [-0.5, 9, 0], "rotation": [-10, 0, 0], "uv": [193, 0]}]}, {"name": "head", "parent": "hyena", "pivot": [0, 14, -7.8], "cubes": [{"origin": [-1, 17.4, -16.8], "size": [1, 6, 6], "pivot": [0, 1.7, -8.4], "rotation": [-10, 0, 0], "uv": [4, 51]}, {"origin": [-4, 12, -19], "size": [8, 8, 6], "inflate": -0.5, "uv": [51, 0]}, {"origin": [3.5, 18.9, -15], "size": [2, 2, 1], "pivot": [3.5, 20.9, -14.5], "rotation": [0, 0, 45], "uv": [37, 9]}, {"origin": [3.5, 20.5, -15], "size": [2, 2, 1], "inflate": -0.1, "pivot": [3.5, 20.9, -14.5], "rotation": [15, 0, 42.5], "uv": [17, 10]}, {"origin": [-3.5, 18.8, -15], "size": [2, 2, 1], "pivot": [-3.5, 20.8, -14.5], "rotation": [0, 0, 45], "uv": [37, 12]}, {"origin": [-5.4, 20.5, -15], "size": [2, 2, 1], "inflate": -0.1, "pivot": [-3.5, 20.8, -14.5], "rotation": [15, 0, -42.5], "uv": [17, 10]}, {"origin": [-2, 13, -22], "size": [4, 3, 4], "inflate": -0.2, "uv": [19, 0]}, {"origin": [-1, 15.1, -22], "size": [2, 1, 1], "inflate": -0.1, "uv": [0, 22]}, {"origin": [-2, 12.5, -21.9], "size": [4, 1, 5], "inflate": -0.3, "pivot": [0, 13.5, -18.9], "rotation": [-5, 0, 0], "uv": [0, 9]}, {"origin": [-2, 14.81, -22.3], "size": [4, 2, 5], "inflate": -0.3, "pivot": [0, 16.9, -19.2], "rotation": [15, 0, 0], "uv": [0, 0]}, {"origin": [-3, 10.3, -12], "size": [6, 6, 6], "inflate": 0.7, "pivot": [0, 10.3, -7], "rotation": [-20, 0, 0], "uv": [90, 2]}, {"origin": [-3, 10.3, -16], "size": [6, 6, 8], "inflate": 0.2, "pivot": [0, 10.3, -7], "rotation": [-20, 0, 0], "uv": [88, 0]}]}]}]}