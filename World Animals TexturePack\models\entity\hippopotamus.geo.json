{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.hippopotamus", "texture_width": 128, "texture_height": 128, "visible_bounds_width": 5, "visible_bounds_height": 2.5, "visible_bounds_offset": [0, 0.75, 0]}, "bones": [{"name": "hippopotamus", "pivot": [0, 0, 0]}, {"name": "body", "parent": "hippopotamus", "pivot": [0, 6, 0], "cubes": [{"origin": [-6, 6, -11], "size": [12, 12, 22], "uv": [0, 0]}, {"origin": [-5, 5, -10], "size": [10, 1, 20], "uv": [0, 34]}, {"origin": [-5, 7, 9.6], "size": [10, 10, 4], "inflate": 0, "pivot": [0, 12, 11], "rotation": [-15, 0, 0], "uv": [30, 55]}]}, {"name": "head", "parent": "body", "pivot": [0, 12.8, -11], "cubes": [{"origin": [-5, 7.8, -16], "size": [10, 10, 5], "uv": [0, 55]}]}, {"name": "cabeza", "parent": "head", "pivot": [0, 17.3, -16], "rotation": [20, 0, 0], "cubes": [{"origin": [-4.5, 8.3, -22], "size": [9, 9, 6], "uv": [46, 0]}, {"origin": [2.5, 17.1, -18], "size": [2, 2, 1], "inflate": -0.025, "pivot": [3.5, 17.3, -17.5], "rotation": [0, -50, 0], "uv": [12, 10]}, {"origin": [-4.5, 17.1, -18], "size": [2, 2, 1], "inflate": -0.025, "pivot": [-3.5, 17.3, -17.5], "rotation": [0, 50, 0], "uv": [12, 0]}]}, {"name": "boca1", "parent": "cabeza", "pivot": [0, 10.8, -22], "cubes": [{"origin": [-3.5, 8.8, -27], "size": [7, 2, 6], "uv": [54, 49]}, {"origin": [1.5, 10.5, -26], "size": [2, 2, 2], "inflate": -0.25, "uv": [70, 0]}, {"origin": [-3.5, 10.5, -26], "size": [2, 2, 2], "inflate": -0.25, "uv": [70, 0]}, {"origin": [-2, 10.2, -27.2], "size": [2, 2, 2], "inflate": -0.55, "uv": [70, 0]}, {"origin": [0, 10.2, -27.2], "size": [2, 2, 2], "inflate": -0.55, "uv": [70, 0]}]}, {"name": "boca0", "parent": "cabeza", "pivot": [0, 16.8, -22], "cubes": [{"origin": [-4, 10.8, -29], "size": [8, 6, 8], "uv": [40, 34]}, {"origin": [-2, 9.6, -26.7], "size": [2, 2, 2], "inflate": -0.55, "uv": [70, 0]}, {"origin": [0, 9.6, -26.7], "size": [2, 2, 2], "inflate": -0.55, "uv": [70, 0]}]}, {"name": "tail", "parent": "body", "pivot": [0, 15, 13.5], "rotation": [-77.5, 0, 0], "cubes": [{"origin": [-0.5, 14.5, 13.5], "size": [1, 1, 5], "uv": [90, 38]}, {"origin": [-0.5, 14.5, 17.7], "size": [1, 1, 6], "inflate": -0.2, "uv": [94, 14]}]}, {"name": "pelo", "parent": "tail", "pivot": [0.25, 15.8, 24.3], "cubes": [{"origin": [-0.5, 14.5, 22.9], "size": [1, 1, 3], "uv": [116, 61]}]}, {"name": "leg0", "parent": "body", "pivot": [4, 10, -8], "cubes": [{"origin": [2, 4, -10], "size": [4, 6, 4], "inflate": 0.1, "uv": [0, 44]}]}, {"name": "leg0_0", "parent": "leg0", "pivot": [4, 4, -8], "cubes": [{"origin": [2, 1, -10], "size": [4, 3, 4], "uv": [42, 69]}, {"origin": [2, 0, -11], "size": [4, 2, 5], "inflate": 0.05, "uv": [64, 29]}, {"origin": [5, 0, -11], "size": [1, 1, 1], "inflate": 0.075, "uv": [4, 20]}, {"origin": [2, 0, -7], "size": [1, 1, 1], "inflate": 0.075, "uv": [18, 11]}, {"origin": [3.5, 0, -11], "size": [1, 1, 1], "inflate": 0.075, "uv": [15, 19]}, {"origin": [2, 0, -11], "size": [1, 1, 1], "inflate": 0.075, "uv": [0, 20]}]}, {"name": "leg1", "parent": "body", "pivot": [-4, 10, -8], "cubes": [{"origin": [-6, 4, -10], "size": [4, 6, 4], "inflate": 0.1, "uv": [0, 34]}]}, {"name": "leg1_0", "parent": "leg1", "pivot": [-4, 4, -8], "cubes": [{"origin": [-6, 1, -10], "size": [4, 3, 4], "uv": [26, 69]}, {"origin": [-6, 0, -11], "size": [4, 2, 5], "inflate": 0.05, "uv": [64, 15]}, {"origin": [-3, 0, -11], "size": [1, 1, 1], "inflate": 0.075, "uv": [18, 0]}, {"origin": [-3, 0, -7], "size": [1, 1, 1], "inflate": 0.075, "uv": [16, 17]}, {"origin": [-4.5, 0, -11], "size": [1, 1, 1], "inflate": 0.075, "uv": [17, 9]}, {"origin": [-6, 0, -11], "size": [1, 1, 1], "inflate": 0.075, "uv": [16, 15]}]}, {"name": "leg2", "parent": "hippopotamus", "pivot": [4, 10, 8], "cubes": [{"origin": [2, 4, 6], "size": [4, 6, 4], "inflate": 0.1, "uv": [0, 10]}]}, {"name": "leg2_0", "parent": "leg2", "pivot": [4, 4, 8], "cubes": [{"origin": [2, 1, 6], "size": [4, 3, 4], "uv": [68, 22]}, {"origin": [2, 0, 5], "size": [4, 2, 5], "inflate": 0.05, "uv": [58, 57]}, {"origin": [5, 0, 5], "size": [1, 1, 1], "inflate": 0.075, "uv": [16, 13]}, {"origin": [2, 0, 9], "size": [1, 1, 1], "inflate": 0.075, "uv": [16, 7]}, {"origin": [3.5, 0, 5], "size": [1, 1, 1], "inflate": 0.075, "uv": [16, 5]}, {"origin": [2, 0, 5], "size": [1, 1, 1], "inflate": 0.075, "uv": [16, 3]}]}, {"name": "leg3", "parent": "hippopotamus", "pivot": [-4, 10, 8], "cubes": [{"origin": [-6, 4, 6], "size": [4, 6, 4], "inflate": 0.1, "uv": [0, 0]}]}, {"name": "leg3_0", "parent": "leg3", "pivot": [-4, 4, 8], "cubes": [{"origin": [-6, 1, 6], "size": [4, 3, 4], "uv": [58, 64]}, {"origin": [-6, 0, 5], "size": [4, 2, 5], "inflate": 0.05, "uv": [46, 15]}, {"origin": [-3, 0, 5], "size": [1, 1, 1], "inflate": 0.075, "uv": [0, 12]}, {"origin": [-3, 0, 9], "size": [1, 1, 1], "inflate": 0.075, "uv": [0, 10]}, {"origin": [-4.5, 0, 5], "size": [1, 1, 1], "inflate": 0.075, "uv": [0, 2]}, {"origin": [-6, 0, 5], "size": [1, 1, 1], "inflate": 0.075, "uv": [0, 0]}]}]}]}