{"format_version": "1.8.0", "minecraft:client_entity": {"description": {"identifier": "pa:<PERSON><PERSON><PERSON><PERSON><PERSON>", "materials": {"default": "turtle"}, "textures": {"default": "textures/entity/pamobile/pa_greatwhiteshark"}, "geometry": {"default": "geometry.pa_great<PERSON><PERSON><PERSON><PERSON>"}, "scripts": {"pre_animation": ["variable.timeMultiplier = query.has_rider ? 0.39972 : 1.0;", "variable.backLegMultiplier = query.has_rider ? 0.5 : 3.0;", "variable.frontLegMultiplier = query.has_rider ? 2.0 : 8.0;", "variable.legSpeedMultiplier = query.has_rider ? 2.0 : 5.0;"], "scale": "1.2"}, "animations": {"general": "animation.pa_greatwhiteshark.general", "move": "animation.pa_greatwhiteshark.move", "ground_move": "animation.pa_greatwhiteshark.ground_move", "look_at_target": "animation.pa_greatwhiteshark.look_at_target"}, "animation_controllers": [{"general": "controller.animation.turtle.general"}, {"move": "controller.animation.turtle.move"}], "render_controllers": ["controller.render.turtle"], "spawn_egg": {"texture": "pa:<PERSON><PERSON><PERSON><PERSON><PERSON>", "texture_index": 0}}}}