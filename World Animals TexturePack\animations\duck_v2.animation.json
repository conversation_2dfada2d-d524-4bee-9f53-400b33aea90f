{"format_version": "1.8.0", "animations": {"animation.duck_v2.idle": {"loop": true, "animation_length": 6, "bones": {"body": {"rotation": {"0.0": [0, 0, 0], "3.0": [-7.5, 0, 0], "6.0": [0, 0, 0]}}}}, "animation.duck_v2.walk": {"loop": true, "animation_length": 0.33333, "bones": {"duck": {"position": {"0.0": [0, 1, 0], "0.1667": [0, 0, 0], "0.3333": [0, 1, 0]}}, "tail": {"rotation": {"0.0": [0, 0, 0], "0.1667": [22.5, 0, 0], "0.3333": [0, 0, 0]}}, "alas1": {"rotation": {"0.0": [0, 0, 12.5], "0.1667": [0, 0, -12.5], "0.3333": [0, 0, 12.5]}}, "alas0": {"rotation": {"0.0": [0, 0, -12.5], "0.1667": [0, 0, 12.5], "0.3333": [0, 0, -12.5]}}, "leg0": {"rotation": {"0.0": [45, 0, 0], "0.1667": [-45, 0, 0], "0.3333": [45, 0, 0]}}, "leg1": {"rotation": {"0.0": [-45, 0, 0], "0.1667": [45, 0, 0], "0.3333": [-45, 0, 0]}}}}, "animation.duck_v2.fly": {"loop": true, "animation_length": 3, "bones": {"duck": {"rotation": {"0.0": [0, 0, 5], "1.0": [-5, -5, 7.5], "2.0": [-2.5, 2, -5], "3.0": [0, 0, 5]}, "position": {"0.0": [0, 0, 0], "1.0": [0, -1, 0], "3.0": [0, 0, 0]}}, "tail": {"rotation": {"0.0": [0, 0, 0], "1.4583": [0, 0, 0], "3.0": [0, 0, 0]}}, "ala1_1": {"rotation": {"0.0": [0, 0, -75], "1.0": [0, 0, -75], "1.2083": [0, 0, -10], "1.4167": [0, 0, -75], "1.625": [0, 0, -10], "1.8333": [0, 0, -75], "2.0417": [0, 0, -10], "2.25": [0, 0, -75], "2.4583": [0, 0, -10], "2.6667": [0, 0, -75], "3.0": [0, 0, -75]}, "scale": {"0.0": [1.5, 1, 1], "1.0": [1.5, 1, 1], "1.4167": [1.5, 1, 1], "1.8333": [1.5, 1, 1], "2.25": [1.5, 1, 1], "2.6667": [1.5, 1, 1], "3.0": [1.5, 1, 1]}}, "ala1_2": {"rotation": {"0.0": [0, 0, 0], "1.0": [0, 0, -12.5], "1.2083": [0, 0, 0], "1.4167": [0, 0, -12.5], "1.625": [0, 0, 0], "1.8333": [0, 0, -12.5], "2.0417": [0, 0, 0], "2.25": [0, 0, -12.5], "2.4583": [0, 0, 0], "2.6667": [0, 0, -12.5], "3.0": [0, 0, 0]}}, "ala0_0": {"rotation": {"0.0": [0, 0, 75], "1.0": [0, 0, 75], "1.2083": [0, 0, 10], "1.4167": [0, 0, 75], "1.625": [0, 0, 10], "1.8333": [0, 0, 75], "2.0417": [0, 0, 10], "2.25": [0, 0, 75], "2.4583": [0, 0, 10], "2.6667": [0, 0, 75], "3.0": [0, 0, 75]}, "scale": {"0.0": [1.5, 1, 1], "1.0": [1.5, 1, 1], "1.4167": [1.5, 1, 1], "1.8333": [1.5, 1, 1], "2.25": [1.5, 1, 1], "3.0": [1.5, 1, 1]}}, "ala0_1": {"rotation": {"0.0": [0, 0, 0], "1.0": [0, 0, 12.5], "1.2083": [0, 0, 0], "1.4167": [0, 0, 12.5], "1.625": [0, 0, 0], "1.8333": [0, 0, 12.5], "2.0417": [0, 0, 0], "2.25": [0, 0, 12.5], "2.4583": [0, 0, 0], "2.6667": [0, 0, 12.5], "3.0": [0, 0, 0]}}, "leg0": {"rotation": {"0.0": [67.5, 0, 0], "3.0": [67.5, 0, 0]}}, "leg1": {"rotation": {"0.0": [67.5, 0, 0], "3.0": [67.5, 0, 0]}}}}, "animation.duck.player.fly": {"loop": true, "animation_length": 3, "bones": {"duck": {"rotation": {"0.0": [0, 0, 5], "1.0": [-5, -5, 7.5], "2.0": [-2.5, 2, -5], "3.0": [0, 0, 5]}, "position": {"0.0": [0, 0, 0], "1.0": [0, -1, 0], "3.0": [0, 0, 0]}}, "tail": {"rotation": {"0.0": [0, 0, 0], "1.4583": [0, 0, 0], "3.0": [0, 0, 0]}}, "ala1_1": {"rotation": {"0.0": [0, 0, -75], "1.0": [0, 0, -75], "1.2083": [0, 0, -10], "1.4167": [0, 0, -75], "1.625": [0, 0, -10], "1.8333": [0, 0, -75], "2.0417": [0, 0, -10], "2.25": [0, 0, -75], "2.4583": [0, 0, -10], "2.6667": [0, 0, -75], "3.0": [0, 0, -75]}, "scale": {"0.0": [1.5, 1, 1], "1.0": [1.5, 1, 1], "1.4167": [1.5, 1, 1], "1.8333": [1.5, 1, 1], "2.25": [1.5, 1, 1], "2.6667": [1.5, 1, 1], "3.0": [1.5, 1, 1]}}, "ala1_2": {"rotation": {"0.0": [0, 0, 0], "1.0": [0, 0, -12.5], "1.2083": [0, 0, 0], "1.4167": [0, 0, -12.5], "1.625": [0, 0, 0], "1.8333": [0, 0, -12.5], "2.0417": [0, 0, 0], "2.25": [0, 0, -12.5], "2.4583": [0, 0, 0], "2.6667": [0, 0, -12.5], "3.0": [0, 0, 0]}}, "ala0_0": {"rotation": {"0.0": [0, 0, 75], "1.0": [0, 0, 75], "1.2083": [0, 0, 10], "1.4167": [0, 0, 75], "1.625": [0, 0, 10], "1.8333": [0, 0, 75], "2.0417": [0, 0, 10], "2.25": [0, 0, 75], "2.4583": [0, 0, 10], "2.6667": [0, 0, 75], "3.0": [0, 0, 75]}, "scale": {"0.0": [1.5, 1, 1], "1.0": [1.5, 1, 1], "1.4167": [1.5, 1, 1], "1.8333": [1.5, 1, 1], "2.25": [1.5, 1, 1], "3.0": [1.5, 1, 1]}}, "ala0_1": {"rotation": {"0.0": [0, 0, 0], "1.0": [0, 0, 12.5], "1.2083": [0, 0, 0], "1.4167": [0, 0, 12.5], "1.625": [0, 0, 0], "1.8333": [0, 0, 12.5], "2.0417": [0, 0, 0], "2.25": [0, 0, 12.5], "2.4583": [0, 0, 0], "2.6667": [0, 0, 12.5], "3.0": [0, 0, 0]}}, "leg0": {"rotation": {"0.0": [67.5, 0, 0], "3.0": [67.5, 0, 0]}}, "leg1": {"rotation": {"0.0": [67.5, 0, 0], "3.0": [67.5, 0, 0]}}, "into_morph": {"rotation": [-70, 0, 0], "position": [0, 0, -5]}}}, "animation.duck_v2.attack": {"animation_length": 0.5, "bones": {"duck": {"rotation": {"0.0": [0, 0, 0], "0.25": [-10, 0, 0], "0.5": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.125": [0, 1, 0], "0.5": [0, 0, 0]}}, "body": {"rotation": {"0.0": [0, 0, 0], "0.125": [-22.5, 0, 0], "0.25": [25, 0, 0], "0.5": [0, 0, 0]}}, "cabeza": {"rotation": {"0.0": [0, 0, 0], "0.125": [-20, 0, 0], "0.25": [0, 0, 0], "0.5": [0, 0, 0]}}, "ala1_1": {"rotation": {"0.0": [0, 0, 0], "0.125": [0, 0, -90], "0.25": [0, 0, 0], "0.375": [0, 0, -90], "0.5": [0, 0, 0]}}, "ala0_0": {"rotation": {"0.0": [0, 0, 0], "0.125": [0, 0, 90], "0.25": [0, 0, 0], "0.375": [0, 0, 100], "0.5": [0, 0, 0]}}, "pico1": {"rotation": {"0.0": [0, 0, 0], "0.1667": [15, 0, 0], "0.2917": [0, 0, 0], "0.5": [0, 0, 0]}}, "pico0": {"rotation": {"0.0": [0, 0, 0], "0.1667": [-7.5, 0, 0], "0.2917": [0, 0, 0], "0.5": [0, 0, 0]}}}}, "animation.duck_v2.on_water": {"loop": true, "bones": {"duck": {"position": [0, -3, 0]}, "leg0": {"rotation": [-65, 0, 0]}, "leg1": {"rotation": [-65, 0, 0]}}}, "animation.duck_v2.baby": {"loop": true, "bones": {"tail": {"scale": [0.65, 1, 0.65]}, "ala1_1": {"scale": [0.9, 0.9, 0.9]}, "ala0_0": {"scale": [0.9, 0.9, 0.9]}, "head": {"scale": [0.9, 0.9, 0.9]}}}}}