{"format_version": "1.8.0", "render_controllers": {"controller.render.ostrich_0": {"arrays": {"textures": {"Array.decor": ["Texture.ostrich_0"]}}, "geometry": "Geometry.default", "part_visibility": [{"*": "query.variant == 0"}], "materials": [{"*": "Material.default"}], "textures": ["Array.decor[query.variant]"]}, "controller.render.ostrich_1": {"arrays": {"textures": {"Array.decor": ["Texture.ostrich_1"]}}, "geometry": "Geometry.default", "part_visibility": [{"*": "query.variant == 1"}], "materials": [{"*": "Material.default"}], "textures": ["Array.decor[query.variant]"]}, "controller.render.ostrich_2": {"arrays": {"textures": {"Array.decor": ["Texture.ostrich_2"]}}, "geometry": "Geometry.default", "part_visibility": [{"*": "query.variant == 2"}], "materials": [{"*": "Material.default"}], "textures": ["Array.decor[query.variant]"]}, "controller.render.ostrich.saddle": {"arrays": {"textures": {"Array.decor": ["Texture.saddle"]}}, "geometry": "Geometry.saddle", "part_visibility": [{"*": "query.is_saddled"}], "materials": [{"*": "Material.default"}], "textures": ["Array.decor[query.variant]"]}, "controller.render.ostrich.green_flag": {"arrays": {"textures": {"Array.decor": ["Texture.flag_green"]}}, "geometry": "Geometry.ostrich_flag", "part_visibility": [{"*": "query.mark_variant == 1"}], "materials": [{"*": "Material.default"}], "textures": ["Array.decor[query.variant]"]}, "controller.render.ostrich.blue_flag": {"arrays": {"textures": {"Array.decor": ["Texture.flag_blue"]}}, "geometry": "Geometry.ostrich_flag", "part_visibility": [{"*": "query.mark_variant == 3"}], "materials": [{"*": "Material.default"}], "textures": ["Array.decor[query.variant]"]}, "controller.render.ostrich.red_flag": {"arrays": {"textures": {"Array.decor": ["Texture.flag_red"]}}, "geometry": "Geometry.ostrich_flag", "part_visibility": [{"*": "query.mark_variant == 2"}], "materials": [{"*": "Material.default"}], "textures": ["Array.decor[query.variant]"]}, "controller.render.ostrich.yellow_flag": {"arrays": {"textures": {"Array.decor": ["Texture.flag_yellow"]}}, "geometry": "Geometry.ostrich_flag", "part_visibility": [{"*": "query.mark_variant == 4"}], "materials": [{"*": "Material.default"}], "textures": ["Array.decor[query.variant]"]}, "controller.render.ostrich.orange_flag": {"arrays": {"textures": {"Array.decor": ["Texture.flag_orange"]}}, "geometry": "Geometry.ostrich_flag", "part_visibility": [{"*": "query.mark_variant == 5"}], "materials": [{"*": "Material.default"}], "textures": ["Array.decor[query.variant]"]}}}