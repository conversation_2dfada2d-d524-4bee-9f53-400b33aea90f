{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.elephant_saddle_v2", "texture_width": 256, "texture_height": 256, "visible_bounds_width": 4, "visible_bounds_height": 4.5, "visible_bounds_offset": [0, 1.75, 0]}, "bones": [{"name": "elephant", "pivot": [0, 0, 1]}, {"name": "body", "parent": "elephant", "pivot": [0, 16, -1]}, {"name": "montura", "parent": "body", "pivot": [0.05, 39.51, 0.5], "rotation": [0, -90, 0], "cubes": [{"origin": [-4.95, 21.3, 7.55], "size": [14, 9, 6], "inflate": 0.45, "uv": [48, 75]}, {"origin": [-5.95, 21.3, -12.45], "size": [15, 9, 6], "inflate": 0.45, "uv": [48, 60]}, {"origin": [-3.95, 29.55, -6.45], "size": [12, 1, 14], "inflate": 1.025, "uv": [50, 0]}, {"origin": [5.05, 20.25, 6.55], "size": [2, 11, 8], "uv": [52, 108]}, {"origin": [5.05, 20.25, -13.45], "size": [2, 11, 8], "uv": [32, 107]}, {"origin": [-3.95, 20.25, 6.55], "size": [2, 11, 8], "uv": [100, 101]}, {"origin": [-3.95, 20.25, -13.45], "size": [2, 11, 8], "uv": [80, 100]}, {"origin": [5.05, 30.65, 6.4], "size": [2, 1, 4], "inflate": -0.025, "uv": [84, 60]}, {"origin": [-3.95, 30.65, 6.4], "size": [2, 1, 4], "inflate": -0.025, "uv": [82, 75]}, {"origin": [5.05, 30.65, -8.3], "size": [2, 1, 4], "inflate": -0.025, "uv": [80, 15]}, {"origin": [-3.95, 30.65, -8.3], "size": [2, 1, 4], "inflate": -0.025, "uv": [58, 37]}, {"origin": [10.05, 29.55, -5.45], "size": [1, 1, 12], "inflate": 1, "uv": [14, 99]}, {"origin": [1.55, 31.25, -5.45], "size": [1, 1, 12], "uv": [0, 98]}, {"origin": [-6.95, 31.25, -5.45], "size": [1, 1, 12], "uv": [97, 88]}, {"origin": [10.05, 31.25, -5.45], "size": [1, 1, 12], "uv": [96, 13]}, {"origin": [-6.95, 29.55, -5.45], "size": [1, 1, 12], "inflate": 1, "uv": [88, 0]}]}]}]}