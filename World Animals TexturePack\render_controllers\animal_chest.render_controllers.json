{"format_version": "1.8.0", "render_controllers": {"controller.render.animal_chest": {"arrays": {"textures": {"Array.base": ["Texture.creamy", "Texture.white", "Texture.brown", "Texture.gray"], "Array.decor": ["Texture.decor_none", "Texture.decor_white", "Texture.decor_orange", "Texture.decor_magenta", "Texture.decor_light_blue", "Texture.decor_yellow", "Texture.decor_lime", "Texture.decor_pink", "Texture.decor_gray", "Texture.decor_silver", "Texture.decor_cyan", "Texture.decor_purple", "Texture.decor_blue", "Texture.decor_brown", "Texture.decor_green", "Texture.decor_red", "Texture.decor_black"]}}, "geometry": "Geometry.default", "part_visibility": [{"chest*": "query.is_chested"}], "materials": [{"*": "Material.default"}], "textures": ["Array.base[query.variant]", "(query.mark_variant == 1 && variable.DecorTextureIndex == 0) ? Texture.decor_wandering_trader : Array.decor[variable.DecorTextureIndex]", "Texture.decor_none"]}}}