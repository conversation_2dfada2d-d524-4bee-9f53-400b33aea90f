{"format_version": "1.13.0", "minecraft:entity": {"description": {"identifier": "worldanimals:pelican", "is_spawnable": true, "is_summonable": true, "is_experimental": false, "animations": {"bird": "animation.bird"}, "scripts": {"animate": ["bird"]}}, "component_groups": {"fly_on": {"minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:mark_variant": {"value": 0}, "minecraft:physics": {"has_gravity": false}, "minecraft:timer": {"looping": false, "time": 20.1, "randomInterval": false, "time_down_event": {"event": "minecraft:fly_off"}}, "minecraft:movement": {"value": 0.35}, "minecraft:movement.glide": {"start_speed": 0.35, "speed_when_turning": 0.45}, "minecraft:follow_range": {"value": 64, "max": 64}, "minecraft:behavior.swoop_attack": {"priority": 2, "delay_range": [10.0, 20.0]}, "minecraft:behavior.circle_around_anchor": {"priority": 3, "radius_range": [5.0, 15.0], "radius_change_chance": 250, "height_above_target_range": [20.0, 40.0], "height_offset_range": [-4.0, 5.0], "height_change_chance": 350, "goal_radius": 1.0}}, "fly_off": {"minecraft:jump.static": {}, "minecraft:mark_variant": {"value": 1}, "minecraft:timer": {"looping": false, "time": 20.1, "randomInterval": false, "time_down_event": {"event": "minecraft:fly_on"}}, "minecraft:can_fly": {"value": true}, "minecraft:navigation.fly": {"can_path_over_water": true, "can_path_from_air": true}, "minecraft:can_climb": {}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.panic": {"priority": 0, "speed_multiplier": 1.25}, "minecraft:movement.fly": {}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}}}, "components": {"minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 5 : 0"}, "minecraft:scale": {"value": 0.75}, "minecraft:timer": {"looping": false, "time": 0.1, "randomInterval": false, "time_down_event": {"event": "minecraft:fly_off"}}, "minecraft:type_family": {"family": ["pelican", "undead", "monster", "mob"]}, "minecraft:loot": {"table": "loot_tables/entities/plumas.json"}, "minecraft:nameable": {}, "minecraft:health": {"value": 10, "max": 10}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:attack": {"damage": 6}, "minecraft:breathable": {"total_supply": 15, "suffocate_time": 0, "breathes_air": true, "breathes_water": false}, "minecraft:collision_box": {"width": 0.9, "height": 0.5}, "minecraft:behavior.nearest_attackable_target": {"priority": 1, "within_radius": 64, "reselect_targets": true, "entity_types": [{"filters": {"test": "is_family", "subject": "other", "value": "fish"}, "max_dist": 64}], "must_see": false, "must_see_forget_duration": 0.5, "scan_interval": 20, "target_search_height": 80.0}}, "events": {"minecraft:entity_spawned": {"randomize": [{"weight": 100, "add": {"component_groups": ["fly_off"]}}]}, "minecraft:fly_off": {"add": {"component_groups": ["fly_off"]}, "remove": {"component_groups": ["fly_on"]}}, "minecraft:fly_on": {"add": {"component_groups": ["fly_on"]}, "remove": {"component_groups": ["fly_off"]}}}}}