{"format_version": "1.10.0", "minecraft:attachable": {"description": {"identifier": "worldanimals:safari_hat.player", "item": {"worldanimals:safari_hat": "query.owner_identifier == 'minecraft:player'"}, "materials": {"default": "armor", "enchanted": "armor_enchanted"}, "textures": {"default": "textures/armor/safari_hat", "enchanted": "textures/misc/enchanted_item_glint"}, "geometry": {"default": "geometry.safari_hat"}, "scripts": {"parent_setup": "variable.boot_layer_visible = 0.0;"}, "render_controllers": ["controller.render.armor"]}}}