{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.caracal", "texture_width": 128, "texture_height": 128, "visible_bounds_width": 4, "visible_bounds_height": 3.5, "visible_bounds_offset": [0, 1.25, 0]}, "bones": [{"name": "caracal", "pivot": [-0.5, 0, 2]}, {"name": "body", "parent": "caracal", "pivot": [-0.5, 10, 1], "cubes": [{"origin": [-3, 6, -6], "size": [6, 5, 12], "uv": [83, 4]}]}, {"name": "tail", "parent": "body", "pivot": [0, 10, 6], "rotation": [-60, 0, 0], "cubes": [{"origin": [-0.5, 9, 5.86603], "size": [1, 1, 6], "uv": [42, 19]}]}, {"name": "tail0", "parent": "tail", "pivot": [0, 10, 11], "rotation": [22.5, 0, 0], "cubes": [{"origin": [-0.5, 8.80665, 11.28376], "size": [1, 1, 6], "inflate": -0.025, "uv": [60, 33]}]}, {"name": "head", "parent": "body", "pivot": [-0.5, 13, -6], "cubes": [{"origin": [-2, 9.5, -7], "size": [4, 5, 4], "pivot": [0.5, 11.03223, -5.03223], "rotation": [36, 0, 0], "uv": [112, 0]}, {"origin": [-3, 12.5, -10], "size": [6, 5, 5], "uv": [106, 21]}, {"origin": [0.75, 17.25, -7], "size": [2, 3, 1], "pivot": [1.75, 17, -6.5], "rotation": [0, -37.5, 10], "uv": [89, 4]}, {"origin": [-2.75, 17.25, -7], "size": [2, 3, 1], "pivot": [-1.75, 17, -6.5], "rotation": [0, 37.5, -10], "uv": [89, 0]}, {"origin": [-2.75, 19.75, -7], "size": [1, 3, 1], "inflate": -0.025, "pivot": [-1.75, 17, -6.5], "rotation": [0, 37.5, -10], "uv": [107, 3]}, {"origin": [1.75, 19.75, -7], "size": [1, 3, 1], "inflate": -0.025, "pivot": [1.75, 17, -6.5], "rotation": [0, -37.5, 10], "uv": [107, 7]}, {"origin": [-1.5, 13.6, -11.75], "size": [3, 1, 2], "inflate": 0.1, "uv": [104, 0]}, {"origin": [-0.5, 14.325, -11.8], "size": [1, 1, 3], "inflate": 0.075, "pivot": [0, 15.1, -10.75], "rotation": [35, 0, 0], "uv": [95, 0]}]}, {"name": "boca", "parent": "head", "pivot": [0, 13.1, -9.75], "cubes": [{"origin": [-1.5, 12.6, -11.75], "size": [3, 1, 2], "uv": [118, 9]}]}, {"name": "cabeza", "parent": "head", "pivot": [-0.5, 17, -5]}, {"name": "panuelo", "parent": "head", "pivot": [0, 10, -4.5], "cubes": [{"origin": [-3.5, 12.53223, -9.53223], "size": [7, 2, 7], "pivot": [-0.5, 13.03223, -5.03223], "rotation": [36, 0, 0], "uv": [0, 84]}, {"origin": [-3.5, 11.53223, -9.53223], "size": [7, 2, 7], "inflate": -0.1, "pivot": [-0.5, 13.03223, -5.03223], "rotation": [36, 0, 0], "uv": [0, 103]}, {"origin": [-0.5, 9.93223, -9.23223], "size": [2, 2, 1], "pivot": [-0.5, 13.03223, -5.03223], "rotation": [36, 0, 0], "uv": [0, 94]}, {"origin": [-0.5, 6.93223, -7.23223], "size": [2, 2, 1], "uv": [0, 94]}, {"origin": [-0.5, 4.93223, -7.23223], "size": [2, 2, 1], "uv": [7, 94]}]}, {"name": "leg3", "parent": "caracal", "pivot": [1.5, 7, 5], "cubes": [{"origin": [1, 0, 5], "size": [2, 4, 2], "uv": [59, 22]}, {"origin": [1, 3.8, 4], "size": [2, 4, 2], "inflate": 0.1, "pivot": [1.5, 7, 6.5], "rotation": [17.5, 0, 0], "uv": [57, 2]}, {"origin": [1, 0, 4], "size": [2, 1, 1], "uv": [82, 0]}]}, {"name": "leg2", "parent": "caracal", "pivot": [-2.5, 7, 5], "cubes": [{"origin": [-3, 0, 5], "size": [2, 4, 2], "uv": [59, 22]}, {"origin": [-3, 3.8, 4], "size": [2, 4, 2], "inflate": 0.1, "pivot": [-2.5, 7, 6.5], "rotation": [17.5, 0, 0], "uv": [57, 2]}, {"origin": [-3, 0, 4], "size": [2, 1, 1], "uv": [82, 0]}]}, {"name": "leg1", "parent": "caracal", "pivot": [1.5, 7, -4.5], "cubes": [{"origin": [1, 0, -6], "size": [2, 4, 2], "uv": [59, 22]}, {"origin": [1, 4, -6], "size": [2, 4, 2], "inflate": 0.1, "uv": [70, 1]}, {"origin": [1, 0, -7], "size": [2, 1, 1], "uv": [82, 0]}]}, {"name": "leg0", "parent": "caracal", "pivot": [-2.5, 7, -4.5], "cubes": [{"origin": [-3, 0, -6], "size": [2, 4, 2], "uv": [59, 22]}, {"origin": [-3, 4, -6], "size": [2, 4, 2], "inflate": 0.1, "uv": [70, 1]}, {"origin": [-3, 0, -7], "size": [2, 1, 1], "uv": [82, 0]}]}]}]}