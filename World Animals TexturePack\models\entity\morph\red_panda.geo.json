{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.red_panda", "texture_width": 64, "texture_height": 64, "visible_bounds_width": 4, "visible_bounds_height": 2.5, "visible_bounds_offset": [0, 0.75, 0]}, "bones": [{"name": "into_morph", "pivot": [0, 0, 0]}, {"name": "red_panda", "parent": "into_morph", "pivot": [0, 3, 0], "cubes": [{"origin": [-4, 5, -6], "size": [8, 6, 13], "uv": [0, 0]}]}, {"name": "tail", "parent": "red_panda", "pivot": [0, 8, 7.2], "rotation": [-12.5, 0, 0], "cubes": [{"origin": [-2, 6, 7.2], "size": [4, 4, 6], "inflate": 0.8, "uv": [29, 0]}]}, {"name": "tail2", "parent": "tail", "pivot": [0, 8, 14.1], "rotation": [12.5, 0, 0], "cubes": [{"origin": [-2, 6, 14.1], "size": [4, 4, 6], "inflate": 0.7, "uv": [22, 27]}]}, {"name": "leg0", "parent": "red_panda", "pivot": [3, 5, 5], "cubes": [{"origin": [1.75, 3.3, 3.5], "size": [3, 4, 3], "inflate": 0.025, "pivot": [3, 3.5, 5], "rotation": [7.5, 0, 0], "uv": [11, 40]}, {"origin": [1.7, 0.8, 3.5], "size": [3, 3, 3], "inflate": -0.2, "uv": [43, 0]}, {"origin": [1.7, 0.1, 2.3], "size": [3, 1, 4], "inflate": 0.1, "uv": [0, 38]}]}, {"name": "leg2", "parent": "red_panda", "pivot": [3, 5, -3], "cubes": [{"origin": [1.75, 3.3, -4.5], "size": [3, 4, 3], "inflate": 0.025, "pivot": [3, 3.5, -3], "rotation": [7.5, 0, 0], "uv": [36, 37]}, {"origin": [1.7, 0.8, -4.5], "size": [3, 3, 3], "inflate": -0.2, "uv": [23, 42]}, {"origin": [1.7, 0.1, -5.7], "size": [3, 1, 4], "inflate": 0.1, "uv": [22, 37]}]}, {"name": "leg1", "parent": "red_panda", "pivot": [-3, 5, 5], "cubes": [{"origin": [-4.25, 3.3, 3.5], "size": [3, 4, 3], "inflate": 0.025, "pivot": [-3, 3.5, 5], "rotation": [7.5, 0, 0], "uv": [36, 26]}, {"origin": [-4.3, 0.8, 3.5], "size": [3, 3, 3], "inflate": -0.2, "uv": [42, 10]}, {"origin": [-4.3, 0.1, 2.3], "size": [3, 1, 4], "inflate": 0.1, "uv": [34, 21]}]}, {"name": "leg3", "parent": "red_panda", "pivot": [-3, 5, -3], "cubes": [{"origin": [-4.25, 3.3, -4.5], "size": [3, 4, 3], "inflate": 0.025, "pivot": [-3, 3.5, -3], "rotation": [7.5, 0, 0], "uv": [0, 0]}, {"origin": [-4.3, 0.8, -4.5], "size": [3, 3, 3], "inflate": -0.2, "uv": [0, 7]}, {"origin": [-4.3, 0.1, -5.7], "size": [3, 1, 4], "inflate": 0.1, "uv": [12, 34]}]}, {"name": "head", "parent": "red_panda", "pivot": [0, 7.5, -6], "cubes": [{"origin": [-4, 5, -11.75], "size": [8, 7, 6], "inflate": 0.25, "uv": [0, 19]}, {"origin": [2, 11, -8], "size": [3, 3, 1], "pivot": [3.5, 12, -7.5], "rotation": [0, 0, 22.5], "uv": [8, 50]}, {"origin": [-5, 11, -8], "size": [3, 3, 1], "pivot": [-3.5, 12, -7.5], "rotation": [0, 0, -22.5], "uv": [0, 50]}]}, {"name": "boca1", "parent": "head", "pivot": [0, 5.7, -12], "cubes": [{"origin": [-2, 5.7, -15], "size": [4, 1, 4], "inflate": 0.075, "uv": [0, 33]}]}, {"name": "boca0", "parent": "head", "pivot": [0, 6, -12], "cubes": [{"origin": [-2, 6, -15], "size": [4, 2, 4], "inflate": 0.1, "uv": [22, 19]}, {"origin": [1.65, 6.2, -14.9], "size": [4, 1, 1], "inflate": -0.35, "pivot": [2.1, 6.7, -14.4], "rotation": [0, -27.5, 67.5], "uv": [54, 1]}, {"origin": [1.65, 6.2, -14.9], "size": [4, 1, 1], "inflate": -0.35, "pivot": [2.1, 6.7, -14.4], "rotation": [0, -27.5, 37.5], "uv": [54, 1]}, {"origin": [1.65, 6.2, -14.9], "size": [4, 1, 1], "inflate": -0.35, "pivot": [2.1, 6.7, -14.4], "rotation": [0, -27.5, 7.5], "uv": [54, 1]}, {"origin": [-5.15, 6.2, -14.9], "size": [4, 1, 1], "inflate": -0.35, "pivot": [-2.1, 6.7, -14.4], "rotation": [0, 27.5, -67.5], "uv": [54, 1]}, {"origin": [-5.25, 6.2, -14.9], "size": [4, 1, 1], "inflate": -0.35, "pivot": [-2.1, 6.7, -14.4], "rotation": [0, 27.5, -37.5], "uv": [54, 1]}, {"origin": [-5.25, 6.2, -14.9], "size": [4, 1, 1], "inflate": -0.35, "pivot": [-2.1, 6.7, -14.4], "rotation": [0, 27.5, -7.5], "uv": [54, 1]}, {"origin": [-0.5, 7, -15], "size": [1, 1, 1], "inflate": 0.125, "uv": [9, 0]}]}, {"name": "root", "pivot": [0, 0, 0]}, {"name": "rotation", "parent": "root", "pivot": [0, 0, 0]}, {"name": "waist2", "parent": "rotation", "pivot": [0, 12, 0]}, {"name": "body_human", "parent": "waist2", "pivot": [0, 24, 0]}, {"name": "leftArm", "parent": "body_human", "pivot": [5, 22, 0]}, {"name": "la", "parent": "leftArm", "pivot": [6, 18, 0]}, {"name": "leftItem", "parent": "la", "pivot": [6, 15, 1]}, {"name": "rightArm", "parent": "body_human", "pivot": [-5, 22, 0]}, {"name": "ra", "parent": "rightArm", "pivot": [-6, 18, 0]}, {"name": "rightItem", "parent": "ra", "pivot": [-6, 17, 2]}]}]}