{"format_version": "1.8.0", "animations": {"animation.penguin_v2.idle": {"loop": true, "animation_length": 2, "bones": {"body": {"rotation": {"0.0": [0, 0, 0], "1.0": [-7.5, 0, 0], "2.0": [0, 0, 0]}}}}, "animation.penguin_v2.walk": {"loop": true, "animation_length": 0.5, "bones": {"penguin": {"rotation": {"0.0": [0, 0, 0], "0.125": [0, 0, -5], "0.25": [0, 0, 0], "0.375": [0, 0, 5], "0.5": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.125": [0, 1, 0], "0.25": [0, 0, 0], "0.375": [0, 1, 0], "0.5": [0, 0, 0]}}, "ala0": {"rotation": {"0.0": [0, 0, 0], "0.125": [0, 0, -27.5], "0.25": [0, 0, 0], "0.375": [0, 0, -27.5], "0.5": [0, 0, 0]}}, "ala1": {"rotation": {"0.0": [0, 0, 0], "0.125": [0, 0, 27.5], "0.25": [0, 0, 0], "0.375": [0, 0, 27.5], "0.5": [0, 0, 0]}}, "leg0": {"rotation": {"0.0": [0, 0, 0], "0.125": [-22.5, 0, 0], "0.25": [0, 0, 0], "0.375": [22.5, 0, 0], "0.5": [0, 0, 0]}}, "leg1": {"rotation": {"0.0": [0, 0, 0], "0.125": [22.5, 0, 0], "0.25": [0, 0, 0], "0.375": [-22.5, 0, 0], "0.5": [0, 0, 0]}}, "tail": {"rotation": {"0.0": [0, 0, 0], "0.125": [-27.5, 0, 0], "0.25": [0, 0, 0], "0.375": [-27.5, 0, 0], "0.5": [0, 0, 0]}}}}, "animation.penguin_v2.dance": {"animation_length": 1, "bones": {"penguin": {"position": {"0.0": [0, 0, 0], "0.1667": [0, 0, 0], "0.3333": [0, 3, 0], "0.5833": [0, 4, 0], "0.8333": [0, 0, 0]}}, "body": {"rotation": {"0.0": [0, 0, 0], "0.1667": [12.5, 0, 0], "0.3333": [-5, 0, 0], "0.5833": [-10, 0, 0], "0.8333": [12.5, 0, 0], "1.0": [0, 0, 0]}}, "ala0": {"rotation": {"0.0": [0, 0, 0], "0.2083": [0, 0, 0], "0.3333": [0, 0, -67.5], "0.4583": [0, 0, 0], "0.5833": [0, 0, -67.5], "0.8333": [0, 0, 0]}}, "ala1": {"rotation": {"0.0": [0, 0, 0], "0.2083": [0, 0, 0], "0.3333": [0, 0, 67.5], "0.4583": [0, 0, 0], "0.5833": [0, 0, 67.5], "0.8333": [0, 0, 0]}}, "leg0": {"rotation": {"0.0": [0, 0, 0], "0.1667": [0, 0, 0], "0.3333": [22.5, 0, 0], "0.5833": [22.5, 0, 0], "0.8333": [0, 0, 0]}}, "leg1": {"rotation": {"0.0": [0, 0, 0], "0.1667": [0, 0, 0], "0.3333": [22.5, 0, 0], "0.5833": [22.5, 0, 0], "0.8333": [0, 0, 0]}}}}, "animation.penguin_v2.sit": {"loop": true, "animation_length": 1, "bones": {"body": {"position": {"0.0": [0, -1, 0], "0.25": [0, -1, 0], "0.5": [0, -1, 0], "0.75": [0, -1, 0], "1.0": [0, -1, 0]}}, "leg0": {"rotation": {"0.0": [-45, 0, 0], "0.25": [-20, 0, 0], "0.5": [-45, 0, 0], "0.75": [-65, 0, 0], "1.0": [-45, 0, 0]}, "position": [0, -0.1, -2]}, "leg1": {"rotation": {"0.0": [-45, 0, 0], "0.25": [-65, 0, 0], "0.5": [-45, 0, 0], "0.75": [-20, 0, 0], "1.0": [-45, 0, 0]}, "position": [0, -0.1, -2]}}}, "animation.blue_penguin.idle": {"loop": true, "animation_length": 2, "bones": {"body": {"rotation": {"0.0": [0, 0, 0], "1.0": [-7.5, 0, 0], "2.0": [0, 0, 0]}}, "bufanda": {"position": [0, 0, -0.7], "scale": [0.65, 1, 0.65]}}}, "animation.penguin_v2.pose": {"loop": true, "bones": {"body": {"position": [0, 7, 0]}, "tail": {"position": [0, 0, 3]}, "cabeza": {"position": [0, 3, 0]}, "ala0": {"position": [5, 0, 0]}, "ala1": {"position": [-5, 0, 0]}}}, "animation.emperor_penguin.idle": {"loop": true, "animation_length": 2, "bones": {"body": {"rotation": {"0.0": [0, 0, 0], "1.0": [-7.5, 0, 0], "2.0": [0, 0, 0]}}, "bufanda": {"rotation": [2.5, 0, 0], "position": [0, 1, -1], "scale": [0.8, 1, 0.8]}}}, "animation.penguin_african.idle": {"loop": true, "animation_length": 2, "bones": {"body": {"rotation": {"0.0": [0, 0, 0], "1.0": [-7.5, 0, 0], "2.0": [0, 0, 0]}}, "bufanda": {"position": [0, -0.1, -0.5], "scale": [0.675, 1, 0.675]}}}, "animation.penguin.swim": {"loop": true, "animation_length": 0.5, "bones": {"penguin": {"rotation": {"0.0": [90, 0, 0], "0.25": [80, 0, 0], "0.5": [90, 0, 0]}, "position": {"0.0": [0, 5, 7], "0.25": [0, 3.8, 7], "0.5": [0, 5, 7]}}, "cabeza": {"rotation": {"0.0": [-75, 0, 0], "0.25": [-80, 0, 0], "0.5": [-75, 0, 0]}, "position": {"0.0": [0, 0, -3], "0.25": [0, 0.5, -3.5], "0.5": [0, 0, -3]}}, "ala0": {"rotation": {"0.0": [0, 0, -45], "0.25": [0, 0, 0], "0.5": [0, 0, -45]}}, "ala1": {"rotation": {"0.0": [0, 0, 45], "0.25": [0, 0, 0], "0.5": [0, 0, 45]}}, "leg0": {"rotation": {"0.0": [-45, 0, 0], "0.25": [45, 0, 0], "0.5": [-45, 0, 0]}}, "leg1": {"rotation": {"0.0": [45, 0, 0], "0.25": [-45, 0, 0], "0.5": [45, 0, 0]}}}}}}