{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.crocodile_loki_hat", "texture_width": 128, "texture_height": 128, "visible_bounds_width": 8, "visible_bounds_height": 2.5, "visible_bounds_offset": [0, 0.75, 0]}, "bones": [{"name": "crocodile", "pivot": [0, 0, 0]}, {"name": "head", "parent": "crocodile", "pivot": [0, 5, -19]}, {"name": "boca0", "parent": "head", "pivot": [0, 4, -23]}, {"name": "boca1", "parent": "head", "pivot": [0, 4, -24]}, {"name": "loki_hat", "parent": "head", "pivot": [0, 8.5, -22], "cubes": [{"origin": [-3, 8.5, -24], "size": [6, 1, 4], "uv": [0, 109]}, {"origin": [-2.5, 8.9, -23.5], "size": [5, 1, 3], "inflate": 0.25, "uv": [0, 104]}]}, {"name": "cuerno_0", "parent": "loki_hat", "pivot": [1.9, 9.1, -23.3], "rotation": [45, -22.5, 0], "cubes": [{"origin": [0.9, 8.95, -24.3], "size": [2, 4, 2], "inflate": -0.25, "uv": [0, 117]}, {"origin": [0.9, 11.55, -25.925], "size": [2, 4, 2], "inflate": -0.35, "pivot": [1.9, 9.1, -23.3], "rotation": [-27.5, 0, 0], "uv": [0, 117]}, {"origin": [0.9, 16.9, -25.125], "size": [2, 4, 2], "inflate": -0.55, "pivot": [1.9, 14.9, -22.3], "rotation": [-82.5, 0, 0], "uv": [0, 117]}, {"origin": [0.9, 14.75, -23.3], "size": [2, 4, 2], "inflate": -0.45, "pivot": [1.9, 14.9, -22.3], "rotation": [-50, 0, 0], "uv": [0, 117]}, {"origin": [0.9, 16.3432, -16.85731], "size": [2, 3, 2], "inflate": -0.8, "pivot": [1.9, 16.62067, -15.17545], "rotation": [-157.5, 0, 0], "uv": [18, 104]}, {"origin": [0.9, 15.17812, -16.38454], "size": [2, 3, 2], "inflate": -0.7, "pivot": [1.9, 16.62067, -15.17545], "rotation": [-125, 0, 0], "uv": [18, 104]}, {"origin": [0.9, 13.78392, -16.54162], "size": [2, 3, 2], "inflate": -0.6, "pivot": [1.9, 16.62067, -15.17545], "rotation": [-102.5, 0, 0], "uv": [18, 104]}]}, {"name": "cuerno_1", "parent": "loki_hat", "pivot": [-2.1, 9.1, -23.3], "rotation": [45, 22.5, 0], "cubes": [{"origin": [-3.1, 8.95, -24.3], "size": [2, 4, 2], "inflate": -0.25, "uv": [0, 117]}, {"origin": [-3.1, 11.55, -25.925], "size": [2, 4, 2], "inflate": -0.35, "pivot": [-2.1, 9.1, -23.3], "rotation": [-27.5, 0, 0], "uv": [0, 117]}, {"origin": [-3.1, 16.9, -25.125], "size": [2, 4, 2], "inflate": -0.55, "pivot": [-2.1, 14.9, -22.3], "rotation": [-82.5, 0, 0], "uv": [0, 117]}, {"origin": [-3.1, 14.75, -23.3], "size": [2, 4, 2], "inflate": -0.45, "pivot": [-2.1, 14.9, -22.3], "rotation": [-50, 0, 0], "uv": [0, 117]}, {"origin": [-3.1, 16.3432, -16.85731], "size": [2, 3, 2], "inflate": -0.8, "pivot": [-2.1, 16.62067, -15.17545], "rotation": [-157.5, 0, 0], "uv": [18, 104]}, {"origin": [-3.1, 15.17812, -16.38454], "size": [2, 3, 2], "inflate": -0.7, "pivot": [-2.1, 16.62067, -15.17545], "rotation": [-125, 0, 0], "uv": [18, 104]}, {"origin": [-3.1, 13.78392, -16.54162], "size": [2, 3, 2], "inflate": -0.6, "pivot": [-2.1, 16.62067, -15.17545], "rotation": [-102.5, 0, 0], "uv": [18, 104]}]}, {"name": "body", "parent": "crocodile", "pivot": [0, 5, -19]}, {"name": "body0", "parent": "body", "pivot": [0, 5, 0]}, {"name": "body1", "parent": "body0", "pivot": [0, 5, 0]}, {"name": "tail", "parent": "body1", "pivot": [0, 5.5, 11.1], "rotation": [-10, 0, 0]}, {"name": "tail2", "parent": "tail", "pivot": [0, 5.5, 20.8], "rotation": [7.5, 0, 0]}, {"name": "tail3", "parent": "tail2", "pivot": [0, 6.3, 32.4], "rotation": [-2.5, 0, 0]}, {"name": "tail4", "parent": "tail3", "pivot": [0, 6.3, 42.4], "rotation": [2.5, 0, 0]}, {"name": "leg3", "parent": "body0", "pivot": [-5, 7, 8]}, {"name": "leg2", "parent": "body0", "pivot": [5, 5, 8]}, {"name": "piernas", "parent": "body", "pivot": [0, 2, -5]}, {"name": "leg4", "parent": "piernas", "pivot": [-5, 7, -9]}, {"name": "leg0", "parent": "piernas", "pivot": [5, 5, -9]}]}]}