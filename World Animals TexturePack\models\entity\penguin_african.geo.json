{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.penguin_african", "texture_width": 64, "texture_height": 64, "visible_bounds_width": 2, "visible_bounds_height": 2.5, "visible_bounds_offset": [0, 0.75, 0]}, "bones": [{"name": "penguin", "pivot": [0, 0, 0]}, {"name": "body", "parent": "penguin", "pivot": [0, 1, 0], "cubes": [{"origin": [-2.5, 1, -2], "size": [5, 8, 4], "pivot": [0, 5, 0], "rotation": [7.5, 0, 0], "uv": [0, 0]}]}, {"name": "bufanda", "parent": "body", "pivot": [0, 10, 0], "rotation": [7.5, 0, 0], "cubes": [{"origin": [-4, 8, -4], "size": [8, 2, 7], "inflate": 0.1, "uv": [0, 37]}, {"origin": [0, 6, -4], "size": [2, 2, 1], "inflate": 0.075, "uv": [0, 37]}, {"origin": [0, 4.85, -4], "size": [2, 1, 1], "inflate": 0.075, "uv": [0, 42]}]}, {"name": "tail", "parent": "body", "pivot": [0, 1, 2], "rotation": [-25, 0, 0], "cubes": [{"origin": [-1.5, 0.5, 1], "size": [3, 2, 2], "inflate": 0.15, "uv": [8, 20]}]}, {"name": "cabeza", "parent": "body", "pivot": [0, 9, 0.5], "cubes": [{"origin": [-2, 9.3, -2.55], "size": [4, 4, 4], "uv": [0, 12]}, {"origin": [-2, 8.5, -2.75], "size": [4, 1, 4], "inflate": 0.25, "pivot": [0, 8.8, -0.3], "rotation": [-2.5, 0, 0], "uv": [14, 8]}, {"origin": [-0.5, 10, -4.75], "size": [1, 1, 3], "pivot": [0, 10.5, -2.55], "rotation": [-12.5, 0, 0], "uv": [15, 22]}, {"origin": [-0.5, 10.6, -5.15], "size": [1, 1, 3], "inflate": 0.05, "pivot": [0, 11.1, -2.65], "rotation": [2.5, 0, 0], "uv": [21, 19]}]}, {"name": "ala0", "parent": "body", "pivot": [2.7, 8, 0], "cubes": [{"origin": [2.4, 2, -1.5], "size": [1, 6, 3], "inflate": -0.1, "uv": [0, 20]}]}, {"name": "ala1", "parent": "body", "pivot": [-2.7, 8, 0], "cubes": [{"origin": [-3.4, 2, -1.5], "size": [1, 6, 3], "inflate": -0.1, "uv": [16, 13]}]}, {"name": "leg0", "parent": "penguin", "pivot": [1.25, 1, -0.5], "cubes": [{"origin": [0.25, 0, -2.5], "size": [2, 1, 3], "uv": [18, 4]}]}, {"name": "leg1", "parent": "penguin", "pivot": [-1.25, 1, -0.5], "cubes": [{"origin": [-2.25, 0, -2.5], "size": [2, 1, 3], "uv": [14, 0]}]}]}]}