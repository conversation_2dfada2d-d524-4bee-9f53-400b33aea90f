{"format_version": "1.10.0", "animation_controllers": {"controller.animation.zombie_tank.arm_movement": {"initial_state": "default", "states": {"attack": {"animations": ["attack"], "transitions": [{"default": "variable.attack_animation_tick <= 0.0"}]}, "default": {"animations": ["move"], "transitions": [{"attack": "variable.attack_animation_tick > 0.0"}, {"flower": "variable.offer_flower_tick"}]}, "flower": {"animations": ["flower"], "transitions": [{"attack": "variable.attack_animation_tick > 0.0"}, {"default": "variable.offer_flower_tick <= 0.0"}]}}}, "controller.animation.zombie_tank.move": {"initial_state": "default", "states": {"default": {"animations": [{"walk": "query.modified_move_speed"}, "look_at_target"]}}}}}