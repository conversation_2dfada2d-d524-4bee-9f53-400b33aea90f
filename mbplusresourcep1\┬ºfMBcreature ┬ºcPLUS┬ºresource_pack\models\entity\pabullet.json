{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.pabullet", "texture_width": 32, "texture_height": 32}, "bones": [{"name": "bone", "pivot": [-0.025, 0.17157, 0.76777], "cubes": [{"origin": [-0.05, 1.17157, -1.23223], "size": [0.05, 1, 1], "pivot": [-0.025, 0.17157, 0.76777], "rotation": [-45, 0, 0], "uv": {"north": {"uv": [0, 0], "uv_size": [8, 8]}, "east": {"uv": [24, 0], "uv_size": [8, 8]}, "south": {"uv": [0, 0], "uv_size": [8, 8]}, "west": {"uv": [0, 0], "uv_size": [8, 8]}, "up": {"uv": [8, 8], "uv_size": [-8, -8]}, "down": {"uv": [0, 8], "uv_size": [8, -8]}}}, {"origin": [-0.05, 1.17157, -0.23223], "size": [0.05, 1, 1], "pivot": [-0.025, 0.17157, 0.76777], "rotation": [-45, 0, 0], "uv": {"north": {"uv": [8, 0], "uv_size": [8, 8]}, "east": {"uv": [16, 0], "uv_size": [8, 8]}, "south": {"uv": [8, 0], "uv_size": [8, 8]}, "west": {"uv": [8, 0], "uv_size": [8, 8]}, "up": {"uv": [16, 8], "uv_size": [-8, -8]}, "down": {"uv": [8, 8], "uv_size": [8, -8]}}}, {"origin": [-0.05, 1.17157, 0.76777], "size": [0.05, 1, 1], "pivot": [-0.025, 0.17157, 0.76777], "rotation": [-45, 0, 0], "uv": {"north": {"uv": [16, 0], "uv_size": [8, 8]}, "east": {"uv": [8, 0], "uv_size": [8, 8]}, "south": {"uv": [16, 0], "uv_size": [8, 8]}, "west": {"uv": [16, 0], "uv_size": [8, 8]}, "up": {"uv": [24, 8], "uv_size": [-8, -8]}, "down": {"uv": [16, 8], "uv_size": [8, -8]}}}, {"origin": [-0.05, 1.17157, 1.76777], "size": [0.05, 1, 1], "pivot": [-0.025, 0.17157, 0.76777], "rotation": [-45, 0, 0], "uv": {"north": {"uv": [24, 0], "uv_size": [8, 8]}, "east": {"uv": [0, 0], "uv_size": [8, 8]}, "south": {"uv": [24, 0], "uv_size": [8, 8]}, "west": {"uv": [24, 0], "uv_size": [8, 8]}, "up": {"uv": [32, 8], "uv_size": [-8, -8]}, "down": {"uv": [24, 8], "uv_size": [8, -8]}}}, {"origin": [-0.05, 0.17157, -1.23223], "size": [0.05, 1, 1], "pivot": [-0.025, 0.17157, 0.76777], "rotation": [-45, 0, 0], "uv": {"north": {"uv": [0, 8], "uv_size": [8, 8]}, "east": {"uv": [24, 8], "uv_size": [8, 8]}, "south": {"uv": [0, 8], "uv_size": [8, 8]}, "west": {"uv": [0, 8], "uv_size": [8, 8]}, "up": {"uv": [8, 16], "uv_size": [-8, -8]}, "down": {"uv": [0, 16], "uv_size": [8, -8]}}}, {"origin": [-0.05, 0.17157, -0.23223], "size": [0.05, 1, 1], "pivot": [-0.025, 0.17157, 0.76777], "rotation": [-45, 0, 0], "uv": {"north": {"uv": [8, 8], "uv_size": [8, 8]}, "east": {"uv": [16, 8], "uv_size": [8, 8]}, "south": {"uv": [8, 8], "uv_size": [8, 8]}, "west": {"uv": [8, 8], "uv_size": [8, 8]}, "up": {"uv": [16, 16], "uv_size": [-8, -8]}, "down": {"uv": [8, 16], "uv_size": [8, -8]}}}, {"origin": [-0.05, 0.17157, 0.76777], "size": [0.05, 1, 1], "pivot": [-0.025, 0.17157, 0.76777], "rotation": [-45, 0, 0], "uv": {"north": {"uv": [16, 8], "uv_size": [8, 8]}, "east": {"uv": [8, 8], "uv_size": [8, 8]}, "south": {"uv": [16, 8], "uv_size": [8, 8]}, "west": {"uv": [16, 8], "uv_size": [8, 8]}, "up": {"uv": [24, 16], "uv_size": [-8, -8]}, "down": {"uv": [16, 16], "uv_size": [8, -8]}}}, {"origin": [-0.05, 0.17157, 1.76777], "size": [0.05, 1, 1], "pivot": [-0.025, 0.17157, 0.76777], "rotation": [-45, 0, 0], "uv": {"north": {"uv": [24, 8], "uv_size": [8, 8]}, "east": {"uv": [0, 8], "uv_size": [8, 8]}, "south": {"uv": [24, 8], "uv_size": [8, 8]}, "west": {"uv": [24, 8], "uv_size": [8, 8]}, "up": {"uv": [32, 16], "uv_size": [-8, -8]}, "down": {"uv": [24, 16], "uv_size": [8, -8]}}}, {"origin": [-0.05, -0.82843, -1.23223], "size": [0.05, 1, 1], "pivot": [-0.025, 0.17157, 0.76777], "rotation": [-45, 0, 0], "uv": {"north": {"uv": [0, 16], "uv_size": [8, 8]}, "east": {"uv": [24, 16], "uv_size": [8, 8]}, "south": {"uv": [0, 16], "uv_size": [8, 8]}, "west": {"uv": [0, 16], "uv_size": [8, 8]}, "up": {"uv": [8, 24], "uv_size": [-8, -8]}, "down": {"uv": [0, 24], "uv_size": [8, -8]}}}, {"origin": [-0.05, -0.82843, -0.23223], "size": [0.05, 1, 1], "pivot": [-0.025, 0.17157, 0.76777], "rotation": [-45, 0, 0], "uv": {"north": {"uv": [8, 16], "uv_size": [8, 8]}, "east": {"uv": [16, 16], "uv_size": [8, 8]}, "south": {"uv": [8, 16], "uv_size": [8, 8]}, "west": {"uv": [8, 16], "uv_size": [8, 8]}, "up": {"uv": [16, 24], "uv_size": [-8, -8]}, "down": {"uv": [8, 24], "uv_size": [8, -8]}}}, {"origin": [-0.05, -0.82843, 0.76777], "size": [0.05, 1, 1], "pivot": [-0.025, 0.17157, 0.76777], "rotation": [-45, 0, 0], "uv": {"north": {"uv": [16, 16], "uv_size": [8, 8]}, "east": {"uv": [7, 16], "uv_size": [8, 8]}, "south": {"uv": [16, 16], "uv_size": [8, 8]}, "west": {"uv": [16, 16], "uv_size": [8, 8]}, "up": {"uv": [24, 24], "uv_size": [-8, -8]}, "down": {"uv": [16, 24], "uv_size": [8, -8]}}}, {"origin": [-0.05, -0.82843, 1.76777], "size": [0.05, 1, 1], "pivot": [-0.025, 0.17157, 0.76777], "rotation": [-45, 0, 0], "uv": {"north": {"uv": [24, 16], "uv_size": [8, 8]}, "east": {"uv": [0, 16], "uv_size": [8, 8]}, "south": {"uv": [24, 16], "uv_size": [8, 8]}, "west": {"uv": [24, 16], "uv_size": [8, 8]}, "up": {"uv": [32, 24], "uv_size": [-8, -8]}, "down": {"uv": [24, 24], "uv_size": [8, -8]}}}, {"origin": [-0.05, -1.82843, -1.23223], "size": [0.05, 1, 1], "pivot": [-0.025, 0.17157, 0.76777], "rotation": [-45, 0, 0], "uv": {"north": {"uv": [0, 24], "uv_size": [8, 8]}, "east": {"uv": [24, 24], "uv_size": [8, 8]}, "south": {"uv": [0, 24], "uv_size": [8, 8]}, "west": {"uv": [0, 24], "uv_size": [8, 8]}, "up": {"uv": [8, 32], "uv_size": [-8, -8]}, "down": {"uv": [0, 32], "uv_size": [8, -8]}}}, {"origin": [-0.05, -1.82843, -0.23223], "size": [0.05, 1, 1], "pivot": [-0.025, 0.17157, 0.76777], "rotation": [-45, 0, 0], "uv": {"north": {"uv": [8, 24], "uv_size": [8, 8]}, "east": {"uv": [16, 24], "uv_size": [8, 8]}, "south": {"uv": [8, 24], "uv_size": [8, 8]}, "west": {"uv": [8, 24], "uv_size": [8, 8]}, "up": {"uv": [16, 32], "uv_size": [-8, -8]}, "down": {"uv": [8, 32], "uv_size": [8, -8]}}}, {"origin": [-0.05, -1.82843, 0.76777], "size": [0.05, 1, 1], "pivot": [-0.025, 0.17157, 0.76777], "rotation": [-45, 0, 0], "uv": {"north": {"uv": [16, 24], "uv_size": [8, 8]}, "east": {"uv": [8, 24], "uv_size": [8, 8]}, "south": {"uv": [16, 24], "uv_size": [8, 8]}, "west": {"uv": [16, 24], "uv_size": [8, 8]}, "up": {"uv": [24, 32], "uv_size": [-8, -8]}, "down": {"uv": [16, 32], "uv_size": [8, -8]}}}, {"origin": [-0.05, -1.82843, 1.76777], "size": [0.05, 1, 1], "pivot": [-0.025, 0.17157, 0.76777], "rotation": [-45, 0, 0], "uv": {"north": {"uv": [24, 24], "uv_size": [8, 8]}, "east": {"uv": [0, 24], "uv_size": [8, 8]}, "south": {"uv": [24, 24], "uv_size": [8, 8]}, "west": {"uv": [24, 24], "uv_size": [8, 8]}, "up": {"uv": [32, 32], "uv_size": [-8, -8]}, "down": {"uv": [24, 32], "uv_size": [8, -8]}}}]}]}, {"description": {"identifier": "geometry.block", "texture_width": 16, "texture_height": 16, "visible_bounds_width": 2, "visible_bounds_height": 2.5, "visible_bounds_offset": [0, 0.75, 0]}, "bones": [{"name": "block_bone", "pivot": [0, 0, 0], "cubes": [{"size": [16, 16, 16], "origin": [-8, 0, -8], "uv": {"north": {"uv": [0, 0], "uv_size": [16, 16]}, "east": {"uv": [0, 0], "uv_size": [16, 16]}, "south": {"uv": [0, 0], "uv_size": [16, 16]}, "west": {"uv": [0, 0], "uv_size": [16, 16]}, "up": {"uv": [0, 0], "uv_size": [16, 16]}, "down": {"uv": [0, 16], "uv_size": [16, -16]}}}]}]}, {"description": {"identifier": "geometry.pa_sapling", "texture_width": 16, "texture_height": 16, "visible_bounds_width": 2, "visible_bounds_height": 2.5, "visible_bounds_offset": [0, 0.75, 0]}, "bones": [{"name": "pa_sapling_bone", "pivot": [0, 0, 0], "cubes": [{"size": [0, 16, 16], "origin": [0, 0, -8], "uv": [0, -16]}, {"size": [16, 16, 0], "origin": [-8, 0, 0], "uv": [0, 0]}]}]}, {"description": {"identifier": "geometry.pa_slab", "texture_width": 16, "texture_height": 16, "visible_bounds_width": 2, "visible_bounds_height": 1.5, "visible_bounds_offset": [0, 0.25, 0]}, "bones": [{"name": "pa_slab_bone", "pivot": [0, 0, 0], "cubes": [{"origin": [-8, 0, -8], "size": [16, 8, 16], "uv": {"north": {"uv": [0, 8], "uv_size": [16, 8]}, "east": {"uv": [0, 8], "uv_size": [16, 8]}, "south": {"uv": [0, 8], "uv_size": [16, 8]}, "west": {"uv": [0, 8], "uv_size": [16, 8]}, "up": {"uv": [0, 0], "uv_size": [16, 16]}, "down": {"uv": [0, 16], "uv_size": [16, -16]}}}]}]}, {"description": {"identifier": "geometry.pa_stair", "texture_width": 16, "texture_height": 16, "visible_bounds_width": 5, "visible_bounds_height": 2.5, "visible_bounds_offset": [0, 0.75, 0]}, "bones": [{"name": "pa_stair_bone", "pivot": [0, 0, 0], "cubes": [{"origin": [8, 0, 9], "size": [0, 0, 0], "uv": {"north": {"uv": [0, 0], "uv_size": [16, 16]}, "east": {"uv": [0, 0], "uv_size": [8, 16]}, "south": {"uv": [0, 0], "uv_size": [16, 16]}, "west": {"uv": [0, 0], "uv_size": [8, 16]}, "up": {"uv": [0, 3], "uv_size": [16, 8]}, "down": {"uv": [0, 8], "uv_size": [16, -8]}}}]}]}, {"description": {"identifier": "geometry.pa_stair_top", "texture_width": 16, "texture_height": 16, "visible_bounds_width": 3, "visible_bounds_height": 2.5, "visible_bounds_offset": [0, 0.75, 0]}, "bones": [{"name": "pa_stair_top_bone", "pivot": [0, 0, -8], "cubes": [{"origin": [-8, 0, -8], "size": [16, 8, 16], "uv": {"north": {"uv": [0, 0], "uv_size": [16, 8]}, "east": {"uv": [0, 0], "uv_size": [8, 8]}, "south": {"uv": [0, 0], "uv_size": [16, 8]}, "west": {"uv": [0, 0], "uv_size": [8, 8]}, "up": {"uv": [0, 0], "uv_size": [16, 16]}, "down": {"uv": [0, 16], "uv_size": [16, -16]}}}, {"origin": [-8, 8, -8], "size": [16, 8, 8], "uv": {"north": {"uv": [0, 0], "uv_size": [16, 8]}, "east": {"uv": [0, 0], "uv_size": [8, 8]}, "south": {"uv": [0, 0], "uv_size": [16, 8]}, "west": {"uv": [0, 0], "uv_size": [8, 8]}, "up": {"uv": [0, 0], "uv_size": [16, 8]}, "down": {"uv": [0, 16], "uv_size": [16, -16]}}}]}]}]}