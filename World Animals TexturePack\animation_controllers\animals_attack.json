{"format_version": "1.10.0", "animation_controllers": {"controller.animation.animals.static": {"initial_state": "static", "states": {"static": {"animations": ["static"]}}}, "controller.animation.animals.walk": {"states": {"walk": {"animations": [{"walk": "query.modified_move_speed"}]}}}, "controller.animation.animals.attack": {"states": {"default": {"transitions": [{"attack": "variable.attack>0"}], "blend_transition": 0.2}, "attack": {"animations": ["attack"], "transitions": [{"default": "query.all_animations_finished"}], "blend_transition": 0.2}}}}}