{"format_version": "1.16.0", "minecraft:entity": {"description": {"identifier": "worldanimals:luc<PERSON><PERSON>", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"minecraft:on": {"minecraft:timer": {"looping": true, "time": [1.0, 3.0], "randomInterval": false, "time_down_event": {"event": "minecraft:off"}}, "minecraft:variant": {"value": 0}}, "minecraft:off": {"minecraft:timer": {"looping": true, "time": [4.0, 8.0], "randomInterval": false, "time_down_event": {"event": "minecraft:on"}}, "minecraft:variant": {"value": 1}}}, "components": {"minecraft:type_family": {"family": ["lucien<PERSON>", "mob"]}, "minecraft:breathable": {"total_supply": 15, "suffocate_time": 0}, "minecraft:timer": {"looping": false, "time": 0.1, "randomInterval": false, "time_down_event": {"event": "minecraft:off"}}, "minecraft:collision_box": {"width": 0.5, "height": 0.5}, "minecraft:damage_sensor": {"triggers": {"cause": "fall", "deals_damage": false}}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}, {"filters": {"test": "in_contact_with_water", "operator": "==", "value": true}, "cause": "drowning", "damage_per_tick": 1}]}, "minecraft:movement": {"value": 0.05}, "minecraft:navigation.float": {"can_path_over_water": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.float_wander": {"xz_dist": 6, "y_dist": 3, "y_offset": -2.0, "random_reselect": true, "float_duration": [0.05, 0.2]}, "minecraft:can_fly": {}, "minecraft:health": {"value": 4, "max": 4}, "minecraft:nameable": {}, "minecraft:physics": {}}, "events": {"minecraft:off": {"add": {"component_groups": ["minecraft:off"]}, "remove": {"component_groups": ["minecraft:on"]}}, "minecraft:on": {"add": {"component_groups": ["minecraft:on"]}, "remove": {"component_groups": ["minecraft:off"]}}}}}