{"format_version": "1.8.0", "minecraft:client_entity": {"description": {"identifier": "worldanimals:tucan", "materials": {"default": "parrot"}, "textures": {"blue": "textures/entity/tucan_v2/tucan_v2", "green": "textures/entity/tucan_v2/tucan_0", "red_blue": "textures/entity/tucan_v2/tucan_1", "yellow_blue": "textures/entity/tucan_v2/tucan_0", "grey": "textures/entity/tucan_v2/tucan_v2"}, "geometry": {"default": "geometry.tucan_v2"}, "scripts": {"pre_animation": ["variable.state = query.is_dancing ? 3 : (query.is_sitting ? 2 : (!query.is_on_ground && !query.is_jumping && !query.is_riding ? 0 : 1));", "variable.dance.x = Math.cos(query.life_time * 57.3 * 20.0);", "variable.dance.y = -Math.sin(query.life_time * 57.3 * 20.0);", "variable.wing_flap = ((math.sin(query.wing_flap_position * 57.3) + 1) * query.wing_flap_speed);"]}, "animations": {"moving": "animation.tucan_v2.idle", "sitting": "animation.tucan_v2.sit", "flying": "animation.tucan_v2.fly", "look_at_target": "animation.common.look_at_target"}, "animation_controllers": [{"setup": "controller.animation.parrot.setup"}, {"move": "controller.animation.parrot.move"}], "render_controllers": ["controller.render.parrot"], "spawn_egg": {"texture": "egg_tucan", "texture_index": 0}}}}