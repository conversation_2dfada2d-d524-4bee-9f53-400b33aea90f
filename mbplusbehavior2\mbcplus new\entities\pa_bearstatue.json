{"format_version": "1.8.0", "minecraft:entity": {"description": {"identifier": "pa:bears<PERSON>ue", "is_spawnable": true, "is_summonable": true, "is_experimental": false, "runtime_identifier": "minecraft:shulker"}, "component_groups": {"minecraft:shulker_purple": {"minecraft:variant": {"value": 5}}, "minecraft:shulker_black": {"minecraft:variant": {"value": 0}}, "minecraft:shulker_blue": {"minecraft:variant": {"value": 4}}, "minecraft:shulker_brown": {"minecraft:variant": {"value": 3}}, "minecraft:shulker_cyan": {"minecraft:variant": {"value": 6}}, "minecraft:shulker_gray": {"minecraft:variant": {"value": 8}}, "minecraft:shulker_green": {"minecraft:variant": {"value": 2}}, "minecraft:shulker_light_blue": {"minecraft:variant": {"value": 12}}, "minecraft:shulker_lime": {"minecraft:variant": {"value": 10}}, "minecraft:shulker_magenta": {"minecraft:variant": {"value": 13}}, "minecraft:shulker_orange": {"minecraft:variant": {"value": 14}}, "minecraft:shulker_pink": {"minecraft:variant": {"value": 9}}, "minecraft:shulker_red": {"minecraft:variant": {"value": 1}}, "minecraft:shulker_silver": {"minecraft:variant": {"value": 7}}, "minecraft:shulker_undyed": {"minecraft:variant": {"value": 16}}, "minecraft:shulker_white": {"minecraft:variant": {"value": 15}}, "minecraft:shulker_yellow": {"minecraft:variant": {"value": 11}}}, "components": {"minecraft:type_family": {"family": ["mob"]}, "minecraft:breathable": {"totalSupply": 99999999, "suffocateTime": 0, "breathesWater": true}, "minecraft:nameable": {"alwaysShow": false, "allowNameTagRenaming": false}, "minecraft:health": {"value": 1, "max": 1}, "minecraft:movement": {"value": 0.0, "max": 0.0}, "minecraft:peek": {"on_open": {"event": "minecraft:on_open"}, "on_close": {"event": "minecraft:on_close"}, "on_target_open": {"event": "minecraft:on_open"}}, "minecraft:behavior.look_at_player": {"priority": 1, "look_distance": 6.0, "probability": 0.02}, "minecraft:behavior.random_look_around": {"priority": 8}, "minecraft:behavior.hurt_by_target": {"priority": 2}, "minecraft:navigation.walk": {}, "minecraft:movement.basic": {}, "minecraft:interact": [{"on_interact": {"filters": {"all_of": [{"any_of": [{"test": "has_equipment", "subject": "other", "domain": "hand", "value": "dye:0"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "dye:16"}]}, {"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_ability", "subject": "other", "value": "instabuild"}]}, "event": "minecraft:turn_black"}, "use_item": true}, {"on_interact": {"filters": {"all_of": [{"test": "has_equipment", "subject": "other", "domain": "hand", "value": "dye:8"}, {"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_ability", "subject": "other", "value": "instabuild"}]}, "event": "minecraft:turn_gray"}, "use_item": true}, {"on_interact": {"filters": {"all_of": [{"test": "has_equipment", "subject": "other", "domain": "hand", "value": "dye:7"}, {"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_ability", "subject": "other", "value": "instabuild"}]}, "event": "minecraft:turn_silver"}, "use_item": true}, {"on_interact": {"filters": {"all_of": [{"any_of": [{"test": "has_equipment", "subject": "other", "domain": "hand", "value": "dye:15"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "dye:19"}]}, {"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_ability", "subject": "other", "value": "instabuild"}]}, "event": "minecraft:turn_white"}, "use_item": true}, {"on_interact": {"filters": {"all_of": [{"test": "has_equipment", "subject": "other", "domain": "hand", "value": "dye:12"}, {"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_ability", "subject": "other", "value": "instabuild"}]}, "event": "minecraft:turn_light_blue"}, "use_item": true}, {"on_interact": {"filters": {"all_of": [{"test": "has_equipment", "subject": "other", "domain": "hand", "value": "dye:14"}, {"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_ability", "subject": "other", "value": "instabuild"}]}, "event": "minecraft:turn_orange"}, "use_item": true}, {"on_interact": {"filters": {"all_of": [{"test": "has_equipment", "subject": "other", "domain": "hand", "value": "dye:1"}, {"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_ability", "subject": "other", "value": "instabuild"}]}, "event": "minecraft:turn_red"}, "use_item": true}, {"on_interact": {"filters": {"all_of": [{"any_of": [{"test": "has_equipment", "subject": "other", "domain": "hand", "value": "dye:4"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "dye:18"}]}, {"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_ability", "subject": "other", "value": "instabuild"}]}, "event": "minecraft:turn_blue"}, "use_item": true}, {"on_interact": {"filters": {"all_of": [{"test": "has_equipment", "subject": "other", "domain": "hand", "value": "dye:5"}, {"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_ability", "subject": "other", "value": "instabuild"}]}, "event": "minecraft:turn_purple"}, "use_item": true}, {"on_interact": {"filters": {"all_of": [{"test": "has_equipment", "subject": "other", "domain": "hand", "value": "dye:13"}, {"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_ability", "subject": "other", "value": "instabuild"}]}, "event": "minecraft:turn_magenta"}, "use_item": true}, {"on_interact": {"filters": {"all_of": [{"test": "has_equipment", "subject": "other", "domain": "hand", "value": "dye:9"}, {"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_ability", "subject": "other", "value": "instabuild"}]}, "event": "minecraft:turn_pink"}, "use_item": true}, {"on_interact": {"filters": {"all_of": [{"any_of": [{"test": "has_equipment", "subject": "other", "domain": "hand", "value": "dye:3"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "dye:17"}]}, {"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_ability", "subject": "other", "value": "instabuild"}]}, "event": "minecraft:turn_brown"}, "use_item": true}, {"on_interact": {"filters": {"all_of": [{"test": "has_equipment", "subject": "other", "domain": "hand", "value": "dye:11"}, {"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_ability", "subject": "other", "value": "instabuild"}]}, "event": "minecraft:turn_yellow"}, "use_item": true}, {"on_interact": {"filters": {"all_of": [{"test": "has_equipment", "subject": "other", "domain": "hand", "value": "dye:10"}, {"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_ability", "subject": "other", "value": "instabuild"}]}, "event": "minecraft:turn_lime"}, "use_item": true}, {"on_interact": {"filters": {"all_of": [{"test": "has_equipment", "subject": "other", "domain": "hand", "value": "dye:2"}, {"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_ability", "subject": "other", "value": "instabuild"}]}, "event": "minecraft:turn_green"}, "use_item": true}, {"on_interact": {"filters": {"all_of": [{"test": "has_equipment", "subject": "other", "domain": "hand", "value": "dye:6"}, {"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_ability", "subject": "other", "value": "instabuild"}]}, "event": "minecraft:turn_cyan"}, "use_item": true}], "minecraft:physics": {}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 10 : 0"}, "minecraft:scale": {"value": 1}, "minecraft:underwater_movement": {"value": 0.02}, "minecraft:collision_box": {"height": 0.0, "width": 0.0}, "minecraft:on_death": {"event": "on:death", "target": "self"}, "minecraft:on_hurt": {"event": "on:hurt", "target": "self"}, "minecraft:on_hurt_by_player": {"event": "on:hurt_by_player", "target": "self"}, "minecraft:on_ignite": {"event": "on:ignite", "target": "self"}, "minecraft:on_target_acquired": {"event": "on:target_acquired", "target": "self"}, "minecraft:on_target_escape": {"event": "on:target_escape", "target": "self"}, "minecraft:on_wake_with_owner": {"event": "on:wake_with_owner", "target": "self"}, "minecraft:loot": {"table": "loot_tables/entities/pa_bearstatue.json"}}, "events": {"minecraft:entity_spawned": {"add": {"component_groups": ["minecraft:shulker_undyed"]}}, "minecraft:turn_purple": {"add": {"component_groups": ["minecraft:shulker_purple"]}}, "minecraft:turn_black": {"add": {"component_groups": ["minecraft:shulker_black"]}}, "minecraft:turn_blue": {"add": {"component_groups": ["minecraft:shulker_blue"]}}, "minecraft:turn_brown": {"add": {"component_groups": ["minecraft:shul<PERSON>_brown"]}}, "minecraft:turn_cyan": {"add": {"component_groups": ["minecraft:shulker_cyan"]}}, "minecraft:turn_gray": {"add": {"component_groups": ["minecraft:shul<PERSON>_gray"]}}, "minecraft:turn_green": {"add": {"component_groups": ["minecraft:shul<PERSON>_green"]}}, "minecraft:turn_light_blue": {"add": {"component_groups": ["minecraft:shulker_light_blue"]}}, "minecraft:turn_lime": {"add": {"component_groups": ["minecraft:shulker_lime"]}}, "minecraft:turn_magenta": {"add": {"component_groups": ["minecraft:shulker_magenta"]}}, "minecraft:turn_orange": {"add": {"component_groups": ["minecraft:shulker_orange"]}}, "minecraft:turn_pink": {"add": {"component_groups": ["minecraft:shul<PERSON>_pink"]}}, "minecraft:turn_red": {"add": {"component_groups": ["minecraft:shulker_red"]}}, "minecraft:turn_silver": {"add": {"component_groups": ["minecraft:shulker_silver"]}}, "minecraft:turn_white": {"add": {"component_groups": ["minecraft:shulker_white"]}}, "minecraft:turn_yellow": {"add": {"component_groups": ["minecraft:shulker_yellow"]}}, "on:death": {"run_command": {"command": []}}, "on:hurt": {"run_command": {"command": []}}, "on:hurt_by_player": {"run_command": {"command": []}}, "on:ignite": {"run_command": {"command": []}}, "on:target_acquired": {"run_command": {"command": []}}, "on:target_escape": {"run_command": {"command": []}}, "on:wake_with_owner": {"run_command": {"command": []}}}}}