{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.orca", "texture_width": 128, "texture_height": 128, "visible_bounds_width": 6, "visible_bounds_height": 3, "visible_bounds_offset": [0, 0.5, 0]}, "bones": [{"name": "orca", "pivot": [0, 0, 0]}, {"name": "tail", "parent": "orca", "pivot": [1, 4, 9.7], "mirror": true, "cubes": [{"origin": [-4.5, 0, 9], "size": [9, 9, 12], "inflate": -0.5, "uv": [36, 31], "mirror": false}, {"origin": [-4.5, 0, 16], "size": [9, 9, 12], "inflate": -0.75, "uv": [35, 10], "mirror": false}, {"origin": [0.5, 3, 27], "size": [8, 1, 12], "pivot": [1, 3, 28], "rotation": [0, 60, 0], "uv": [65, 61], "mirror": false}, {"origin": [-8.5, 3, 27], "size": [8, 1, 12], "pivot": [-1, 3, 28], "rotation": [0, -60, 0], "uv": [65, 9], "mirror": false}]}, {"name": "body", "parent": "orca", "pivot": [1, 0, -10], "mirror": true, "cubes": [{"origin": [-5, 0, -10], "size": [10, 9, 13], "inflate": 0.25, "uv": [0, 0], "mirror": false}, {"origin": [-5, 7.2, -14], "size": [10, 2, 13], "inflate": -0.1, "pivot": [1, 8, -11], "rotation": [5, 0, 0], "uv": [31, 52], "mirror": false}, {"origin": [-4.5, 7.75, -1.8], "size": [9, 2, 12], "inflate": -0.15, "pivot": [1, 9, 2], "rotation": [-5, 0, 0], "uv": [0, 57], "mirror": false}, {"origin": [-5, 0, 2], "size": [10, 9, 8], "inflate": -0.25, "uv": [34, 67], "mirror": false}]}, {"name": "cabeza", "parent": "body", "pivot": [1, 0, -9], "mirror": true, "cubes": [{"origin": [-5, 4, -22], "size": [10, 5, 12], "uv": [0, 40], "mirror": false}]}, {"name": "boca", "parent": "cabeza", "pivot": [0, 4, -12], "cubes": [{"origin": [-5.5, 0, -22.5], "size": [11, 4, 13], "uv": [0, 22]}]}, {"name": "back_fin", "parent": "body", "pivot": [1, 6, -6], "rotation": [-60, 0, 0], "mirror": true, "cubes": [{"origin": [-0.5, 6.98563, -8.10373], "size": [1, 9, 9], "pivot": [0, 0, -7], "rotation": [10, 0, 0], "uv": [68, 22], "mirror": false}, {"origin": [-0.5, 8.48198, -4.51821], "size": [1, 9, 5], "inflate": -0.1, "pivot": [0, 0, -7], "rotation": [30, 0, 0], "uv": [12, 71], "mirror": false}, {"origin": [-0.5, 1.15698, 4.15679], "size": [1, 9, 5], "inflate": -0.15, "pivot": [0, 0, -7], "rotation": [65, 0, 0], "uv": [0, 71], "mirror": false}]}, {"name": "left_fin", "parent": "body", "pivot": [-4, 1, -8], "rotation": [0, 25, -20], "mirror": true, "cubes": [{"origin": [-19.7033, 1.68404, -9.79426], "size": [16, 1, 8], "uv": [64, 52], "mirror": false}]}, {"name": "right_fin", "parent": "body", "pivot": [4, 1, -8], "rotation": [0, -25, 20], "mirror": true, "cubes": [{"origin": [4, 1, -9], "size": [16, 1, 8], "uv": [33, 0], "mirror": false}]}]}]}