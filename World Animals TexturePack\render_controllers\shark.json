{"format_version": "1.8.0", "render_controllers": {"controller.render.hammerhead_shark_0": {"arrays": {"textures": {"Array.decor": ["Texture.default"]}}, "geometry": "Geometry.default", "part_visibility": [{"*": "query.variant == 0"}], "materials": [{"*": "Material.default"}], "textures": ["Array.decor[query.variant]"]}, "controller.render.hammerhead_shark_1": {"arrays": {"textures": {"Array.decor": ["Texture.hammerhead_shark_1"]}}, "geometry": "Geometry.default", "part_visibility": [{"*": "query.variant == 1"}], "materials": [{"*": "Material.default"}], "textures": ["Array.decor[query.variant]"]}, "controller.render.hammerhead_shark_2": {"arrays": {"textures": {"Array.decor": ["Texture.hammerhead_shark_2"]}}, "geometry": "Geometry.default", "part_visibility": [{"*": "query.variant == 2"}], "materials": [{"*": "Material.default"}], "textures": ["Array.decor[query.variant]"]}, "controller.render.hammerhead_shark_3": {"arrays": {"textures": {"Array.decor": ["Texture.hammerhead_shark_3"]}}, "geometry": "Geometry.default", "part_visibility": [{"*": "query.variant == 3"}], "materials": [{"*": "Material.default"}], "textures": ["Array.decor[query.variant]"]}, "controller.render.shark": {"arrays": {"textures": {"Array.decor": ["Texture.default"]}}, "geometry": "Geometry.default", "part_visibility": [{"*": "query.variant == 0"}], "materials": [{"*": "Material.default"}], "textures": ["Array.decor[query.variant]"]}, "controller.render.shark1": {"arrays": {"textures": {"Array.decor": ["Texture.shark_1"]}}, "geometry": "Geometry.default", "part_visibility": [{"*": "query.variant == 1"}], "materials": [{"*": "Material.default"}], "textures": ["Array.decor[query.variant]"]}}}